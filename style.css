
      img:is([sizes="auto" i], [sizes^="auto," i]) {
        contain-intrinsic-size: 3000px 1500px;
      }
      img.emoji {
        display: inline !important;
        border: none !important;
        box-shadow: none !important;
        height: 1em !important;
        width: 1em !important;
        margin: 0 0.07em !important;
        vertical-align: -0.1em !important;
        background: 0 0 !important;
        padding: 0 !important;
      }
      :where(.wp-block-button__link) {
        border-radius: 9999px;
        box-shadow: none;
        padding: calc(0.667em + 2px) calc(1.333em + 2px);
        text-decoration: none;
      }
      :root :where(.wp-block-button .wp-block-button__link.is-style-outline),
      :root :where(.wp-block-button.is-style-outline > .wp-block-button__link) {
        border: 2px solid;
        padding: 0.667em 1.333em;
      }
      :root
        :where(
          .wp-block-button
            .wp-block-button__link.is-style-outline:not(.has-text-color)
        ),
      :root
        :where(
          .wp-block-button.is-style-outline
            > .wp-block-button__link:not(.has-text-color)
        ) {
        color: currentColor;
      }
      :root
        :where(
          .wp-block-button
            .wp-block-button__link.is-style-outline:not(.has-background)
        ),
      :root
        :where(
          .wp-block-button.is-style-outline
            > .wp-block-button__link:not(.has-background)
        ) {
        background-color: initial;
        background-image: none;
      }
      :where(.wp-block-calendar table:not(.has-background) th) {
        background: #ddd;
      }
      :where(.wp-block-columns) {
        margin-bottom: 1.75em;
      }
      :where(.wp-block-columns.has-background) {
        padding: 1.25em 2.375em;
      }
      :where(.wp-block-post-comments input[type="submit"]) {
        border: none;
      }
      :where(.wp-block-cover-image:not(.has-text-color)),
      :where(.wp-block-cover:not(.has-text-color)) {
        color: #fff;
      }
      :where(.wp-block-cover-image.is-light:not(.has-text-color)),
      :where(.wp-block-cover.is-light:not(.has-text-color)) {
        color: #000;
      }
      :root :where(.wp-block-cover h1:not(.has-text-color)),
      :root :where(.wp-block-cover h2:not(.has-text-color)),
      :root :where(.wp-block-cover h3:not(.has-text-color)),
      :root :where(.wp-block-cover h4:not(.has-text-color)),
      :root :where(.wp-block-cover h5:not(.has-text-color)),
      :root :where(.wp-block-cover h6:not(.has-text-color)),
      :root :where(.wp-block-cover p:not(.has-text-color)) {
        color: inherit;
      }
      :where(.wp-block-file) {
        margin-bottom: 1.5em;
      }
      :where(.wp-block-file__button) {
        border-radius: 2em;
        display: inline-block;
        padding: 0.5em 1em;
      }
      :where(.wp-block-file__button):is(a):active,
      :where(.wp-block-file__button):is(a):focus,
      :where(.wp-block-file__button):is(a):hover,
      :where(.wp-block-file__button):is(a):visited {
        box-shadow: none;
        color: #fff;
        opacity: 0.85;
        text-decoration: none;
      }
      :where(.wp-block-group.wp-block-group-is-layout-constrained) {
        position: relative;
      }
      :root
        :where(
          .wp-block-image.is-style-rounded img,
          .wp-block-image .is-style-rounded img
        ) {
        border-radius: 9999px;
      }
      :where(
          .wp-block-latest-comments:not(
              [style*="line-height"] .wp-block-latest-comments__comment
            )
        ) {
        line-height: 1.1;
      }
      :where(
          .wp-block-latest-comments:not(
              [style*="line-height"]
                .wp-block-latest-comments__comment-excerpt
                p
            )
        ) {
        line-height: 1.8;
      }
      :root :where(.wp-block-latest-posts.is-grid) {
        padding: 0;
      }
      :root :where(.wp-block-latest-posts.wp-block-latest-posts__list) {
        padding-left: 0;
      }
      ul {
        box-sizing: border-box;
      }
      :root :where(.wp-block-list.has-background) {
        padding: 1.25em 2.375em;
      }
      :where(
          .wp-block-navigation.has-background
            .wp-block-navigation-item
            a:not(.wp-element-button)
        ),
      :where(
          .wp-block-navigation.has-background
            .wp-block-navigation-submenu
            a:not(.wp-element-button)
        ) {
        padding: 0.5em 1em;
      }
      :where(
          .wp-block-navigation
            .wp-block-navigation__submenu-container
            .wp-block-navigation-item
            a:not(.wp-element-button)
        ),
      :where(
          .wp-block-navigation
            .wp-block-navigation__submenu-container
            .wp-block-navigation-submenu
            a:not(.wp-element-button)
        ),
      :where(
          .wp-block-navigation
            .wp-block-navigation__submenu-container
            .wp-block-navigation-submenu
            button.wp-block-navigation-item__content
        ),
      :where(
          .wp-block-navigation
            .wp-block-navigation__submenu-container
            .wp-block-pages-list__item
            button.wp-block-navigation-item__content
        ) {
        padding: 0.5em 1em;
      }
      :root :where(p.has-background) {
        padding: 1.25em 2.375em;
      }
      :where(p.has-text-color:not(.has-link-color)) a {
        color: inherit;
      }
      :where(.wp-block-post-comments-form) input:not([type="submit"]),
      :where(.wp-block-post-comments-form) textarea {
        border: 1px solid #949494;
        font-family: inherit;
        font-size: 1em;
      }
      :where(.wp-block-post-comments-form)
        input:where(:not([type="submit"]):not([type="checkbox"])),
      :where(.wp-block-post-comments-form) textarea {
        padding: calc(0.667em + 2px);
      }
      :where(.wp-block-post-excerpt) {
        box-sizing: border-box;
        margin-bottom: var(--wp--style--block-gap);
        margin-top: var(--wp--style--block-gap);
      }
      :where(.wp-block-preformatted.has-background) {
        padding: 1.25em 2.375em;
      }
      :where(.wp-block-search__button) {
        border: 1px solid #ccc;
        padding: 6px 10px;
      }
      :where(.wp-block-search__input) {
        font-family: inherit;
        font-size: inherit;
        font-style: inherit;
        font-weight: inherit;
        letter-spacing: inherit;
        line-height: inherit;
        text-transform: inherit;
      }
      :where(.wp-block-search__button-inside .wp-block-search__inside-wrapper) {
        border: 1px solid #949494;
        box-sizing: border-box;
        padding: 4px;
      }
      :where(.wp-block-search__button-inside .wp-block-search__inside-wrapper)
        .wp-block-search__input {
        border: none;
        border-radius: 0;
        padding: 0 4px;
      }
      :where(.wp-block-search__button-inside .wp-block-search__inside-wrapper)
        .wp-block-search__input:focus {
        outline: 0;
      }
      :where(.wp-block-search__button-inside .wp-block-search__inside-wrapper)
        :where(.wp-block-search__button) {
        padding: 4px 8px;
      }
      :root :where(.wp-block-separator.is-style-dots) {
        height: auto;
        line-height: 1;
        text-align: center;
      }
      :root :where(.wp-block-separator.is-style-dots):before {
        color: currentColor;
        content: "···";
        font-family: serif;
        font-size: 1.5em;
        letter-spacing: 2em;
        padding-left: 2em;
      }
      :root :where(.wp-block-site-logo.is-style-rounded) {
        border-radius: 9999px;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link {
        background-color: #f0f0f0;
        color: #444;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-amazon {
        background-color: #f90;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-bandcamp {
        background-color: #1ea0c3;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-behance {
        background-color: #0757fe;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-bluesky {
        background-color: #0a7aff;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-codepen {
        background-color: #1e1f26;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-deviantart {
        background-color: #02e49b;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-discord {
        background-color: #5865f2;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-dribbble {
        background-color: #e94c89;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-dropbox {
        background-color: #4280ff;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-etsy {
        background-color: #f45800;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-facebook {
        background-color: #0866ff;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-fivehundredpx {
        background-color: #000;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-flickr {
        background-color: #0461dd;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-foursquare {
        background-color: #e65678;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-github {
        background-color: #24292d;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-goodreads {
        background-color: #eceadd;
        color: #382110;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-google {
        background-color: #ea4434;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-gravatar {
        background-color: #1d4fc4;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-instagram {
        background-color: #f00075;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-lastfm {
        background-color: #e21b24;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-linkedin {
        background-color: #0d66c2;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-mastodon {
        background-color: #3288d4;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-medium {
        background-color: #000;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-meetup {
        background-color: #f6405f;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-patreon {
        background-color: #000;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-pinterest {
        background-color: #e60122;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-pocket {
        background-color: #ef4155;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-reddit {
        background-color: #ff4500;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-skype {
        background-color: #0478d7;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-snapchat {
        background-color: #fefc00;
        color: #fff;
        stroke: #000;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-soundcloud {
        background-color: #ff5600;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-spotify {
        background-color: #1bd760;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-telegram {
        background-color: #2aabee;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-threads {
        background-color: #000;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-tiktok {
        background-color: #000;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-tumblr {
        background-color: #011835;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-twitch {
        background-color: #6440a4;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-twitter {
        background-color: #1da1f2;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-vimeo {
        background-color: #1eb7ea;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-vk {
        background-color: #4680c2;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-wordpress {
        background-color: #3499cd;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-whatsapp {
        background-color: #25d366;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-x {
        background-color: #000;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-yelp {
        background-color: #d32422;
        color: #fff;
      }
      :where(.wp-block-social-links:not(.is-style-logos-only))
        .wp-social-link-youtube {
        background-color: red;
        color: #fff;
      }
      :where(.wp-block-social-links.is-style-logos-only) .wp-social-link {
        background: 0 0;
      }
      :where(.wp-block-social-links.is-style-logos-only) .wp-social-link svg {
        height: 1.25em;
        width: 1.25em;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-amazon {
        color: #f90;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-bandcamp {
        color: #1ea0c3;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-behance {
        color: #0757fe;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-bluesky {
        color: #0a7aff;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-codepen {
        color: #1e1f26;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-deviantart {
        color: #02e49b;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-discord {
        color: #5865f2;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-dribbble {
        color: #e94c89;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-dropbox {
        color: #4280ff;
      }
      :where(.wp-block-social-links.is-style-logos-only) .wp-social-link-etsy {
        color: #f45800;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-facebook {
        color: #0866ff;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-fivehundredpx {
        color: #000;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-flickr {
        color: #0461dd;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-foursquare {
        color: #e65678;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-github {
        color: #24292d;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-goodreads {
        color: #382110;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-google {
        color: #ea4434;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-gravatar {
        color: #1d4fc4;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-instagram {
        color: #f00075;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-lastfm {
        color: #e21b24;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-linkedin {
        color: #0d66c2;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-mastodon {
        color: #3288d4;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-medium {
        color: #000;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-meetup {
        color: #f6405f;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-patreon {
        color: #000;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-pinterest {
        color: #e60122;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-pocket {
        color: #ef4155;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-reddit {
        color: #ff4500;
      }
      :where(.wp-block-social-links.is-style-logos-only) .wp-social-link-skype {
        color: #0478d7;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-snapchat {
        color: #fff;
        stroke: #000;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-soundcloud {
        color: #ff5600;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-spotify {
        color: #1bd760;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-telegram {
        color: #2aabee;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-threads {
        color: #000;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-tiktok {
        color: #000;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-tumblr {
        color: #011835;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-twitch {
        color: #6440a4;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-twitter {
        color: #1da1f2;
      }
      :where(.wp-block-social-links.is-style-logos-only) .wp-social-link-vimeo {
        color: #1eb7ea;
      }
      :where(.wp-block-social-links.is-style-logos-only) .wp-social-link-vk {
        color: #4680c2;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-whatsapp {
        color: #25d366;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-wordpress {
        color: #3499cd;
      }
      :where(.wp-block-social-links.is-style-logos-only) .wp-social-link-x {
        color: #000;
      }
      :where(.wp-block-social-links.is-style-logos-only) .wp-social-link-yelp {
        color: #d32422;
      }
      :where(.wp-block-social-links.is-style-logos-only)
        .wp-social-link-youtube {
        color: red;
      }
      :root :where(.wp-block-social-links .wp-social-link a) {
        padding: 0.25em;
      }
      :root
        :where(.wp-block-social-links.is-style-logos-only .wp-social-link a) {
        padding: 0;
      }
      :root
        :where(.wp-block-social-links.is-style-pill-shape .wp-social-link a) {
        padding-left: 0.6666666667em;
        padding-right: 0.6666666667em;
      }
      :root :where(.wp-block-tag-cloud.is-style-outline) {
        display: flex;
        flex-wrap: wrap;
        gap: 1ch;
      }
      :root :where(.wp-block-tag-cloud.is-style-outline a) {
        border: 1px solid;
        font-size: unset !important;
        margin-right: 0;
        padding: 1ch 2ch;
        text-decoration: none !important;
      }
      :root :where(.wp-block-table-of-contents) {
        box-sizing: border-box;
      }
      :where(.wp-block-term-description) {
        box-sizing: border-box;
        margin-bottom: var(--wp--style--block-gap);
        margin-top: var(--wp--style--block-gap);
      }
      :where(pre.wp-block-verse) {
        font-family: inherit;
      }
      :root {
        --wp--preset--font-size--normal: 16px;
        --wp--preset--font-size--huge: 42px;
      }
      html :where(.has-border-color) {
        border-style: solid;
      }
      html :where([style*="border-top-color"]) {
        border-top-style: solid;
      }
      html :where([style*="border-right-color"]) {
        border-right-style: solid;
      }
      html :where([style*="border-bottom-color"]) {
        border-bottom-style: solid;
      }
      html :where([style*="border-left-color"]) {
        border-left-style: solid;
      }
      html :where([style*="border-width"]) {
        border-style: solid;
      }
      html :where([style*="border-top-width"]) {
        border-top-style: solid;
      }
      html :where([style*="border-right-width"]) {
        border-right-style: solid;
      }
      html :where([style*="border-bottom-width"]) {
        border-bottom-style: solid;
      }
      html :where([style*="border-left-width"]) {
        border-left-style: solid;
      }
      html :where(img[class*="wp-image-"]) {
        height: auto;
        max-width: 100%;
      }
      :where(figure) {
        margin: 0 0 1em;
      }
      html :where(.is-position-sticky) {
        --wp-admin--admin-bar--position-offset: var(
          --wp-admin--admin-bar--height,
          0px
        );
      }
      @media screen and (max-width: 600px) {
        html :where(.is-position-sticky) {
          --wp-admin--admin-bar--position-offset: 0px;
        }
      }
      :root :where(.wp-block-image figcaption) {
        color: #555;
        font-size: 13px;
        text-align: center;
      }
      :where(.wp-block-group.has-background) {
        padding: 1.25em 2.375em;
      }
      :root :where(.wp-block-template-part.has-background) {
        margin-bottom: 0;
        margin-top: 0;
        padding: 1.25em 2.375em;
      }
      :root {
        --wp--preset--aspect-ratio--square: 1;
        --wp--preset--aspect-ratio--4-3: 4/3;
        --wp--preset--aspect-ratio--3-4: 3/4;
        --wp--preset--aspect-ratio--3-2: 3/2;
        --wp--preset--aspect-ratio--2-3: 2/3;
        --wp--preset--aspect-ratio--16-9: 16/9;
        --wp--preset--aspect-ratio--9-16: 9/16;
        --wp--preset--color--black: #000000;
        --wp--preset--color--cyan-bluish-gray: #abb8c3;
        --wp--preset--color--white: #ffffff;
        --wp--preset--color--pale-pink: #f78da7;
        --wp--preset--color--vivid-red: #cf2e2e;
        --wp--preset--color--luminous-vivid-orange: #ff6900;
        --wp--preset--color--luminous-vivid-amber: #fcb900;
        --wp--preset--color--light-green-cyan: #7bdcb5;
        --wp--preset--color--vivid-green-cyan: #00d084;
        --wp--preset--color--pale-cyan-blue: #8ed1fc;
        --wp--preset--color--vivid-cyan-blue: #0693e3;
        --wp--preset--color--vivid-purple: #9b51e0;
        --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(
          135deg,
          rgba(6, 147, 227, 1) 0%,
          rgb(155, 81, 224) 100%
        );
        --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(
          135deg,
          rgb(122, 220, 180) 0%,
          rgb(0, 208, 130) 100%
        );
        --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(
          135deg,
          rgba(252, 185, 0, 1) 0%,
          rgba(255, 105, 0, 1) 100%
        );
        --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(
          135deg,
          rgba(255, 105, 0, 1) 0%,
          rgb(207, 46, 46) 100%
        );
        --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(
          135deg,
          rgb(238, 238, 238) 0%,
          rgb(169, 184, 195) 100%
        );
        --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(
          135deg,
          rgb(74, 234, 220) 0%,
          rgb(151, 120, 209) 20%,
          rgb(207, 42, 186) 40%,
          rgb(238, 44, 130) 60%,
          rgb(251, 105, 98) 80%,
          rgb(254, 248, 76) 100%
        );
        --wp--preset--gradient--blush-light-purple: linear-gradient(
          135deg,
          rgb(255, 206, 236) 0%,
          rgb(152, 150, 240) 100%
        );
        --wp--preset--gradient--blush-bordeaux: linear-gradient(
          135deg,
          rgb(254, 205, 165) 0%,
          rgb(254, 45, 45) 50%,
          rgb(107, 0, 62) 100%
        );
        --wp--preset--gradient--luminous-dusk: linear-gradient(
          135deg,
          rgb(255, 203, 112) 0%,
          rgb(199, 81, 192) 50%,
          rgb(65, 88, 208) 100%
        );
        --wp--preset--gradient--pale-ocean: linear-gradient(
          135deg,
          rgb(255, 245, 203) 0%,
          rgb(182, 227, 212) 50%,
          rgb(51, 167, 181) 100%
        );
        --wp--preset--gradient--electric-grass: linear-gradient(
          135deg,
          rgb(202, 248, 128) 0%,
          rgb(113, 206, 126) 100%
        );
        --wp--preset--gradient--midnight: linear-gradient(
          135deg,
          rgb(2, 3, 129) 0%,
          rgb(40, 116, 252) 100%
        );
        --wp--preset--font-size--small: 13px;
        --wp--preset--font-size--medium: 20px;
        --wp--preset--font-size--large: 36px;
        --wp--preset--font-size--x-large: 42px;
        --wp--preset--spacing--20: 0.44rem;
        --wp--preset--spacing--30: 0.67rem;
        --wp--preset--spacing--40: 1rem;
        --wp--preset--spacing--50: 1.5rem;
        --wp--preset--spacing--60: 2.25rem;
        --wp--preset--spacing--70: 3.38rem;
        --wp--preset--spacing--80: 5.06rem;
        --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
        --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
        --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
        --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1),
          6px 6px rgba(0, 0, 0, 1);
        --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
      }
      :where(.is-layout-flex) {
        gap: 0.5em;
      }
      :where(.is-layout-grid) {
        gap: 0.5em;
      }
      :where(.wp-block-post-template.is-layout-flex) {
        gap: 1.25em;
      }
      :where(.wp-block-post-template.is-layout-grid) {
        gap: 1.25em;
      }
      :where(.wp-block-columns.is-layout-flex) {
        gap: 2em;
      }
      :where(.wp-block-columns.is-layout-grid) {
        gap: 2em;
      }
      :root :where(.wp-block-pullquote) {
        font-size: 1.5em;
        line-height: 1.6;
      }
      :root {
        --bs-blue: #0d6efd;
        --bs-indigo: #6610f2;
        --bs-purple: #6f42c1;
        --bs-pink: #d63384;
        --bs-red: #dc3545;
        --bs-orange: #fd7e14;
        --bs-yellow: #ffc107;
        --bs-green: #198754;
        --bs-teal: #20c997;
        --bs-cyan: #0dcaf0;
        --bs-black: #000;
        --bs-white: #fff;
        --bs-gray: #6c757d;
        --bs-gray-dark: #343a40;
        --bs-gray-100: #f8f9fa;
        --bs-gray-200: #e9ecef;
        --bs-gray-300: #dee2e6;
        --bs-gray-400: #ced4da;
        --bs-gray-500: #adb5bd;
        --bs-gray-600: #6c757d;
        --bs-gray-700: #495057;
        --bs-gray-800: #343a40;
        --bs-gray-900: #212529;
        --bs-primary: #0d6efd;
        --bs-secondary: #6c757d;
        --bs-success: #198754;
        --bs-info: #0dcaf0;
        --bs-warning: #ffc107;
        --bs-danger: #dc3545;
        --bs-light: #f8f9fa;
        --bs-dark: #212529;
        --bs-primary-rgb: 13, 110, 253;
        --bs-secondary-rgb: 108, 117, 125;
        --bs-success-rgb: 25, 135, 84;
        --bs-info-rgb: 13, 202, 240;
        --bs-warning-rgb: 255, 193, 7;
        --bs-danger-rgb: 220, 53, 69;
        --bs-light-rgb: 248, 249, 250;
        --bs-dark-rgb: 33, 37, 41;
        --bs-primary-text-emphasis: #052c65;
        --bs-secondary-text-emphasis: #2b2f32;
        --bs-success-text-emphasis: #0a3622;
        --bs-info-text-emphasis: #055160;
        --bs-warning-text-emphasis: #664d03;
        --bs-danger-text-emphasis: #58151c;
        --bs-light-text-emphasis: #495057;
        --bs-dark-text-emphasis: #495057;
        --bs-primary-bg-subtle: #cfe2ff;
        --bs-secondary-bg-subtle: #e2e3e5;
        --bs-success-bg-subtle: #d1e7dd;
        --bs-info-bg-subtle: #cff4fc;
        --bs-warning-bg-subtle: #fff3cd;
        --bs-danger-bg-subtle: #f8d7da;
        --bs-light-bg-subtle: #fcfcfd;
        --bs-dark-bg-subtle: #ced4da;
        --bs-primary-border-subtle: #9ec5fe;
        --bs-secondary-border-subtle: #c4c8cb;
        --bs-success-border-subtle: #a3cfbb;
        --bs-info-border-subtle: #9eeaf9;
        --bs-warning-border-subtle: #ffe69c;
        --bs-danger-border-subtle: #f1aeb5;
        --bs-light-border-subtle: #e9ecef;
        --bs-dark-border-subtle: #adb5bd;
        --bs-white-rgb: 255, 255, 255;
        --bs-black-rgb: 0, 0, 0;
        --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif;
        --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas,
          "Liberation Mono", "Courier New", monospace;
        --bs-gradient: linear-gradient(
          180deg,
          hsla(0, 0%, 100%, 0.15),
          hsla(0, 0%, 100%, 0)
        );
        --bs-body-font-family: system-ui, -apple-system, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif;
        --bs-body-font-size: 1rem;
        --bs-body-font-weight: 400;
        --bs-body-line-height: 1.5;
        --bs-body-color: #212529;
        --bs-body-color-rgb: 33, 37, 41;
        --bs-body-bg: #fff;
        --bs-body-bg-rgb: 255, 255, 255;
        --bs-emphasis-color: #000;
        --bs-emphasis-color-rgb: 0, 0, 0;
        --bs-secondary-color: rgba(33, 37, 41, 0.75);
        --bs-secondary-color-rgb: 33, 37, 41;
        --bs-secondary-bg: #e9ecef;
        --bs-secondary-bg-rgb: 233, 236, 239;
        --bs-tertiary-color: rgba(33, 37, 41, 0.5);
        --bs-tertiary-color-rgb: 33, 37, 41;
        --bs-tertiary-bg: #f8f9fa;
        --bs-tertiary-bg-rgb: 248, 249, 250;
        --bs-heading-color: inherit;
        --bs-link-color: #0d6efd;
        --bs-link-color-rgb: 13, 110, 253;
        --bs-link-decoration: none;
        --bs-link-hover-color: #0a58ca;
        --bs-link-hover-color-rgb: 10, 88, 202;
        --bs-code-color: #d63384;
        --bs-highlight-color: #212529;
        --bs-highlight-bg: #fff3cd;
        --bs-border-width: 1px;
        --bs-border-style: solid;
        --bs-border-color: #dee2e6;
        --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
        --bs-border-radius: 0.375rem;
        --bs-border-radius-sm: 0.25rem;
        --bs-border-radius-lg: 0.5rem;
        --bs-border-radius-xl: 1rem;
        --bs-border-radius-xxl: 2rem;
        --bs-border-radius-2xl: var(--bs-border-radius-xxl);
        --bs-border-radius-pill: 50rem;
        --bs-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --bs-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);
        --bs-focus-ring-width: 0.25rem;
        --bs-focus-ring-opacity: 0.25;
        --bs-focus-ring-color: rgba(13, 110, 253, 0.25);
        --bs-form-valid-color: #198754;
        --bs-form-valid-border-color: #198754;
        --bs-form-invalid-color: #dc3545;
        --bs-form-invalid-border-color: #dc3545;
      }
      *,
      :after,
      :before {
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
      }
      @media (prefers-reduced-motion: no-preference) {
        :root {
          scroll-behavior: smooth;
        }
      }
      body {
        background-color: var(--bs-body-bg);
        color: var(--bs-body-color);
        font-family: var(--bs-body-font-family);
        font-size: var(--bs-body-font-size);
        font-weight: var(--bs-body-font-weight);
        line-height: var(--bs-body-line-height);
        margin: 0;
        text-align: var(--bs-body-text-align);
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
      }
      hr {
        border: 0;
        border-top: var(--bs-border-width) solid;
        color: inherit;
        margin: 1rem 0;
        opacity: 0.25;
      }
      h1,
      h2,
      h3 {
        color: var(--bs-heading-color);
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: 0.5rem;
        margin-top: 0;
      }
      h1 {
        font-size: calc(1.375rem + 1.5vw);
      }
      @media (min-width: 1200px) {
        h1 {
          font-size: 2.5rem;
        }
      }
      h2 {
        font-size: calc(1.325rem + 0.9vw);
      }
      @media (min-width: 1200px) {
        h2 {
          font-size: 2rem;
        }
      }
      h3 {
        font-size: calc(1.3rem + 0.6vw);
      }
      @media (min-width: 1200px) {
        h3 {
          font-size: 1.75rem;
        }
      }
      p {
        margin-bottom: 1rem;
        margin-top: 0;
      }
      address {
        font-style: normal;
        line-height: inherit;
        margin-bottom: 1rem;
      }
      ul {
        padding-left: 2rem;
      }
      ul {
        margin-bottom: 1rem;
        margin-top: 0;
      }
      ul ul {
        margin-bottom: 0;
      }
      b,
      strong {
        font-weight: bolder;
      }
      a {
        color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
        text-decoration: none;
      }
      a:hover {
        --bs-link-color-rgb: var(--bs-link-hover-color-rgb);
      }
      a:not([href]):not([class]),
      a:not([href]):not([class]):hover {
        color: inherit;
        text-decoration: none;
      }
      img,
      svg {
        vertical-align: middle;
      }
      table {
        border-collapse: collapse;
        caption-side: bottom;
      }
      th {
        text-align: inherit;
        text-align: -webkit-match-parent;
      }
      tbody,
      td,
      th,
      thead,
      tr {
        border: 0 solid;
        border-color: inherit;
      }
      button {
        border-radius: 0;
      }
      button:focus:not(:focus-visible) {
        outline: 0;
      }
      button,
      input,
      select,
      textarea {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        margin: 0;
      }
      button,
      select {
        text-transform: none;
      }
      [role="button"] {
        cursor: pointer;
      }
      select {
        word-wrap: normal;
      }
      select:disabled {
        opacity: 1;
      }
      [type="button"],
      [type="reset"],
      [type="submit"],
      button {
        -webkit-appearance: button;
      }
      [type="button"]:not(:disabled),
      [type="reset"]:not(:disabled),
      [type="submit"]:not(:disabled),
      button:not(:disabled) {
        cursor: pointer;
      }
      ::-moz-focus-inner {
        border-style: none;
        padding: 0;
      }
      textarea {
        resize: vertical;
      }
      ::-webkit-datetime-edit-day-field,
      ::-webkit-datetime-edit-fields-wrapper,
      ::-webkit-datetime-edit-hour-field,
      ::-webkit-datetime-edit-minute,
      ::-webkit-datetime-edit-month-field,
      ::-webkit-datetime-edit-text,
      ::-webkit-datetime-edit-year-field {
        padding: 0;
      }
      ::-webkit-inner-spin-button {
        height: auto;
      }
      ::-webkit-search-decoration {
        -webkit-appearance: none;
      }
      ::-webkit-color-swatch-wrapper {
        padding: 0;
      }
      ::-webkit-file-upload-button {
        -webkit-appearance: button;
        font: inherit;
      }
      ::file-selector-button {
        -webkit-appearance: button;
        font: inherit;
      }
      summary {
        cursor: pointer;
        display: list-item;
      }
      [hidden] {
        display: none !important;
      }
      .img-fluid {
        height: auto;
        max-width: 100%;
      }
      .container {
        --bs-gutter-x: 1.5rem;
        --bs-gutter-y: 0;
        margin-left: auto;
        margin-right: auto;
        padding-left: calc(var(--bs-gutter-x) * 0.5);
        padding-right: calc(var(--bs-gutter-x) * 0.5);
        width: 100%;
      }
      @media (min-width: 576px) {
        .container {
          max-width: 540px;
        }
      }
      @media (min-width: 768px) {
        .container {
          max-width: 720px;
        }
      }
      @media (min-width: 992px) {
        .container {
          max-width: 960px;
        }
      }
      @media (min-width: 1200px) {
        .container {
          max-width: 1140px;
        }
      }
      @media (min-width: 1400px) {
        .container {
          max-width: 1320px;
        }
      }
      :root {
        --bs-breakpoint-xs: 0;
        --bs-breakpoint-sm: 576px;
        --bs-breakpoint-md: 768px;
        --bs-breakpoint-lg: 992px;
        --bs-breakpoint-xl: 1200px;
        --bs-breakpoint-xxl: 1400px;
      }
      .row {
        --bs-gutter-x: 1.5rem;
        --bs-gutter-y: 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        margin-left: calc(var(--bs-gutter-x) * -0.5);
        margin-right: calc(var(--bs-gutter-x) * -0.5);
        margin-top: calc(var(--bs-gutter-y) * -1);
      }
      .row > * {
        -ms-flex-negative: 0;
        flex-shrink: 0;
        margin-top: var(--bs-gutter-y);
        max-width: 100%;
        padding-left: 4px;
        padding-right: 4px;
        width: 100%;
      }
      .col-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
      }
      .col-6 {
        width: 50%;
      }
      @media (min-width: 576px) {
        .col-sm-12 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-sm-12 {
          width: 100%;
        }
      }
      @media (min-width: 768px) {
        .col-md-2 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-md-2 {
          width: 16.66666667%;
        }
        .col-md-4 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-md-4 {
          width: 33.33333333%;
        }
        .col-md-6 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-md-6 {
          width: 50%;
        }
        .col-md-8 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-md-8 {
          width: 66.66666667%;
        }
        .col-md-12 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-md-12 {
          width: 100%;
        }
      }
      @media (min-width: 992px) {
        .col-lg-2 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-lg-2 {
          width: 16.66666667%;
        }
        .col-lg-3 {
          width: 25%;
        }
        .col-lg-3,
        .col-lg-4 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-lg-4 {
          width: 33.33333333%;
        }
        .col-lg-5 {
          width: 41.66666667%;
        }
        .col-lg-5,
        .col-lg-6 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-lg-6 {
          width: 50%;
        }
        .col-lg-8 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-lg-8 {
          width: 66.66666667%;
        }
        .col-lg-12 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-lg-12 {
          width: 100%;
        }
      }
      @media (min-width: 1200px) {
        .col-xl-2 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xl-2 {
          width: 16.66666667%;
        }
        .col-xl-3 {
          width: 25%;
        }
        .col-xl-3,
        .col-xl-4 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xl-4 {
          width: 33.33333333%;
        }
        .col-xl-6 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xl-6 {
          width: 50%;
        }
        .col-xl-8 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xl-8 {
          width: 66.66666667%;
        }
        .col-xl-12 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xl-12 {
          width: 100%;
        }
      }
      @media (min-width: 1400px) {
        .col-xxl-2 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xxl-2 {
          width: 16.66666667%;
        }
        .col-xxl-3 {
          width: 25%;
        }
        .col-xxl-3,
        .col-xxl-4 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xxl-4 {
          width: 33.33333333%;
        }
        .col-xxl-6 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xxl-6 {
          width: 50%;
        }
        .col-xxl-8 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xxl-8 {
          width: 66.66666667%;
        }
        .col-xxl-12 {
          -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
          flex: 0 0 auto;
        }
        .col-xxl-12 {
          width: 100%;
        }
      }
      .table {
        --bs-table-color-type: initial;
        --bs-table-bg-type: initial;
        --bs-table-color-state: initial;
        --bs-table-bg-state: initial;
        --bs-table-color: var(--bs-emphasis-color);
        --bs-table-bg: var(--bs-body-bg);
        --bs-table-border-color: var(--bs-border-color);
        --bs-table-accent-bg: transparent;
        --bs-table-striped-color: var(--bs-emphasis-color);
        --bs-table-striped-bg: rgba(var(--bs-emphasis-color-rgb), 0.05);
        --bs-table-active-color: var(--bs-emphasis-color);
        --bs-table-active-bg: rgba(var(--bs-emphasis-color-rgb), 0.1);
        --bs-table-hover-color: var(--bs-emphasis-color);
        --bs-table-hover-bg: rgba(var(--bs-emphasis-color-rgb), 0.075);
        border-color: var(--bs-table-border-color);
        margin-bottom: 1rem;
        vertical-align: top;
        width: 100%;
      }
      .table > :not(caption) > * > * {
        background-color: var(--bs-table-bg);
        border-bottom-width: var(--bs-border-width);
        -webkit-box-shadow: inset 0 0 0 9999px
          var(
            --bs-table-bg-state,
            var(--bs-table-bg-type, var(--bs-table-accent-bg))
          );
        box-shadow: inset 0 0 0 9999px
          var(
            --bs-table-bg-state,
            var(--bs-table-bg-type, var(--bs-table-accent-bg))
          );
        color: var(
          --bs-table-color-state,
          var(--bs-table-color-type, var(--bs-table-color))
        );
        padding: 0.5rem;
      }
      .table > tbody {
        vertical-align: inherit;
      }
      .table > thead {
        vertical-align: bottom;
      }
      .table-light {
        --bs-table-color: #000;
        --bs-table-bg: #f8f9fa;
        --bs-table-border-color: #c6c7c8;
        --bs-table-striped-bg: #ecedee;
        --bs-table-striped-color: #000;
        --bs-table-active-bg: #dfe0e1;
        --bs-table-active-color: #000;
        --bs-table-hover-bg: #e5e6e7;
        --bs-table-hover-color: #000;
      }
      .table-light {
        border-color: var(--bs-table-border-color);
        color: var(--bs-table-color);
      }
      .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
      }
      .btn {
        --bs-btn-padding-x: 0.75rem;
        --bs-btn-padding-y: 0.375rem;
        --bs-btn-font-family: ;
        --bs-btn-font-size: 1rem;
        --bs-btn-font-weight: 400;
        --bs-btn-line-height: 1.5;
        --bs-btn-color: var(--bs-body-color);
        --bs-btn-bg: transparent;
        --bs-btn-border-width: var(--bs-border-width);
        --bs-btn-border-color: transparent;
        --bs-btn-border-radius: var(--bs-border-radius);
        --bs-btn-hover-border-color: transparent;
        --bs-btn-box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, 0.15),
          0 1px 1px rgba(0, 0, 0, 0.075);
        --bs-btn-disabled-opacity: 0.65;
        --bs-btn-focus-box-shadow: 0 0 0 0.25rem
          rgba(var(--bs-btn-focus-shadow-rgb), 0.5);
        background-color: var(--bs-btn-bg);
        background-image: var(--bs-gradient);
        border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
        border-radius: var(--bs-btn-border-radius);
        -webkit-box-shadow: var(--bs-btn-box-shadow);
        box-shadow: var(--bs-btn-box-shadow);
        color: var(--bs-btn-color);
        cursor: pointer;
        display: inline-block;
        font-family: var(--bs-btn-font-family);
        font-size: var(--bs-btn-font-size);
        font-weight: var(--bs-btn-font-weight);
        line-height: var(--bs-btn-line-height);
        padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
        text-align: center;
        -webkit-transition: color 0.15s ease-in-out,
          background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
          -webkit-box-shadow 0.15s ease-in-out;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
          border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
          border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
          border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
          -webkit-box-shadow 0.15s ease-in-out;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        vertical-align: middle;
      }
      @media (prefers-reduced-motion: reduce) {
        .btn {
          -webkit-transition: none;
          transition: none;
        }
      }
      .btn:hover {
        background-color: var(--bs-btn-hover-bg);
        border-color: var(--bs-btn-hover-border-color);
        color: var(--bs-btn-hover-color);
      }
      .btn:focus-visible {
        background-color: var(--bs-btn-hover-bg);
        background-image: var(--bs-gradient);
        border-color: var(--bs-btn-hover-border-color);
        -webkit-box-shadow: var(--bs-btn-box-shadow),
          var(--bs-btn-focus-box-shadow);
        box-shadow: var(--bs-btn-box-shadow), var(--bs-btn-focus-box-shadow);
        color: var(--bs-btn-hover-color);
        outline: 0;
      }
      .btn.active,
      .btn.show,
      .btn:first-child:active,
      :not(.btn-check) + .btn:active {
        background-color: var(--bs-btn-active-bg);
        background-image: none;
        border-color: var(--bs-btn-active-border-color);
        -webkit-box-shadow: var(--bs-btn-active-shadow);
        box-shadow: var(--bs-btn-active-shadow);
        color: var(--bs-btn-active-color);
      }
      .btn.active:focus-visible,
      .btn.show:focus-visible,
      .btn:first-child:active:focus-visible,
      :not(.btn-check) + .btn:active:focus-visible {
        -webkit-box-shadow: var(--bs-btn-active-shadow),
          var(--bs-btn-focus-box-shadow);
        box-shadow: var(--bs-btn-active-shadow), var(--bs-btn-focus-box-shadow);
      }
      .btn.disabled,
      .btn:disabled {
        background-color: var(--bs-btn-disabled-bg);
        background-image: none;
        border-color: var(--bs-btn-disabled-border-color);
        -webkit-box-shadow: none;
        box-shadow: none;
        color: var(--bs-btn-disabled-color);
        opacity: var(--bs-btn-disabled-opacity);
        pointer-events: none;
      }
      .btn-light {
        --bs-btn-color: #000;
        --bs-btn-bg: #f8f9fa;
        --bs-btn-border-color: #f8f9fa;
        --bs-btn-hover-color: #000;
        --bs-btn-hover-bg: #d3d4d5;
        --bs-btn-hover-border-color: #c6c7c8;
        --bs-btn-focus-shadow-rgb: 211, 212, 213;
        --bs-btn-active-color: #000;
        --bs-btn-active-bg: #c6c7c8;
        --bs-btn-active-border-color: #babbbc;
        --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
        --bs-btn-disabled-color: #000;
        --bs-btn-disabled-bg: #f8f9fa;
        --bs-btn-disabled-border-color: #f8f9fa;
      }
      .btn-dark {
        --bs-btn-color: #fff;
        --bs-btn-bg: #212529;
        --bs-btn-border-color: #212529;
        --bs-btn-hover-color: #fff;
        --bs-btn-hover-bg: #424649;
        --bs-btn-hover-border-color: #373b3e;
        --bs-btn-focus-shadow-rgb: 66, 70, 73;
        --bs-btn-active-color: #fff;
        --bs-btn-active-bg: #4d5154;
        --bs-btn-active-border-color: #373b3e;
        --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
        --bs-btn-disabled-color: #fff;
        --bs-btn-disabled-bg: #212529;
        --bs-btn-disabled-border-color: #212529;
      }
      .btn-link {
        --bs-btn-font-weight: 400;
        --bs-btn-color: var(--bs-link-color);
        --bs-btn-bg: transparent;
        --bs-btn-border-color: transparent;
        --bs-btn-hover-color: var(--bs-link-hover-color);
        --bs-btn-hover-border-color: transparent;
        --bs-btn-active-color: var(--bs-link-hover-color);
        --bs-btn-active-border-color: transparent;
        --bs-btn-disabled-color: #6c757d;
        --bs-btn-disabled-border-color: transparent;
        --bs-btn-box-shadow: 0 0 0 #000;
        --bs-btn-focus-shadow-rgb: 49, 132, 253;
        background-image: none;
        text-decoration: none;
      }
      .btn-link:focus-visible {
        color: var(--bs-btn-color);
      }
      .btn-link:hover {
        color: var(--bs-btn-hover-color);
      }
      .fade {
        -webkit-transition: opacity 0.15s linear;
        transition: opacity 0.15s linear;
      }
      @media (prefers-reduced-motion: reduce) {
        .fade {
          -webkit-transition: none;
          transition: none;
        }
      }
      .fade:not(.show) {
        opacity: 0;
      }
      .collapse:not(.show) {
        display: none;
      }
      .collapsing {
        height: 0;
        overflow: hidden;
        -webkit-transition: height 0.35s;
        transition: height 0.35s ease;
      }
      @media (prefers-reduced-motion: reduce) {
        .collapsing {
          -webkit-transition: none;
          transition: none;
        }
      }
      .collapsing.collapse-horizontal {
        height: auto;
        -webkit-transition: width 0.35s;
        transition: width 0.35s ease;
        width: 0;
      }
      @media (prefers-reduced-motion: reduce) {
        .collapsing.collapse-horizontal {
          -webkit-transition: none;
          transition: none;
        }
      }
      .dropdown,
      .dropdown-center,
      .dropend,
      .dropstart,
      .dropup,
      .dropup-center {
        position: relative;
      }
      .dropdown-toggle {
        white-space: nowrap;
      }
      .dropdown-toggle:after {
        border-bottom: 0;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
        border-top: 0.3em solid;
        content: "";
        display: inline-block;
        margin-left: 0.255em;
        vertical-align: 0.255em;
      }
      .dropdown-toggle:empty:after {
        margin-left: 0;
      }
      .dropdown-menu {
        --bs-dropdown-zindex: 1000;
        --bs-dropdown-min-width: 10rem;
        --bs-dropdown-padding-x: 0;
        --bs-dropdown-padding-y: 0.5rem;
        --bs-dropdown-spacer: 0.125rem;
        --bs-dropdown-font-size: 1rem;
        --bs-dropdown-color: var(--bs-body-color);
        --bs-dropdown-bg: var(--bs-body-bg);
        --bs-dropdown-border-color: var(--bs-border-color-translucent);
        --bs-dropdown-border-radius: var(--bs-border-radius);
        --bs-dropdown-border-width: var(--bs-border-width);
        --bs-dropdown-inner-border-radius: calc(
          var(--bs-border-radius) - var(--bs-border-width)
        );
        --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
        --bs-dropdown-divider-margin-y: 0.5rem;
        --bs-dropdown-box-shadow: var(--bs-box-shadow);
        --bs-dropdown-link-color: var(--bs-body-color);
        --bs-dropdown-link-hover-color: var(--bs-body-color);
        --bs-dropdown-link-hover-bg: var(--bs-tertiary-bg);
        --bs-dropdown-link-active-color: #fff;
        --bs-dropdown-link-active-bg: #0d6efd;
        --bs-dropdown-link-disabled-color: var(--bs-tertiary-color);
        --bs-dropdown-item-padding-x: 1rem;
        --bs-dropdown-item-padding-y: 0.25rem;
        --bs-dropdown-header-color: #6c757d;
        --bs-dropdown-header-padding-x: 1rem;
        --bs-dropdown-header-padding-y: 0.5rem;
        background-clip: padding-box;
        background-color: var(--bs-dropdown-bg);
        border: var(--bs-dropdown-border-width) solid
          var(--bs-dropdown-border-color);
        border-radius: var(--bs-dropdown-border-radius);
        -webkit-box-shadow: var(--bs-dropdown-box-shadow);
        box-shadow: var(--bs-dropdown-box-shadow);
        color: var(--bs-dropdown-color);
        display: none;
        font-size: var(--bs-dropdown-font-size);
        list-style: none;
        margin: 0;
        min-width: var(--bs-dropdown-min-width);
        padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
        position: absolute;
        text-align: left;
        z-index: var(--bs-dropdown-zindex);
      }
      .dropup .dropdown-toggle:after {
        border-bottom: 0.3em solid;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
        border-top: 0;
        content: "";
        display: inline-block;
        margin-left: 0.255em;
        vertical-align: 0.255em;
      }
      .dropup .dropdown-toggle:empty:after {
        margin-left: 0;
      }
      .dropend .dropdown-toggle:after {
        border-bottom: 0.3em solid transparent;
        border-left: 0.3em solid;
        border-right: 0;
        border-top: 0.3em solid transparent;
        content: "";
        display: inline-block;
        margin-left: 0.255em;
        vertical-align: 0.255em;
      }
      .dropend .dropdown-toggle:empty:after {
        margin-left: 0;
      }
      .dropend .dropdown-toggle:after {
        vertical-align: 0;
      }
      .dropstart .dropdown-toggle:after {
        content: "";
        display: inline-block;
        display: none;
        margin-left: 0.255em;
        vertical-align: 0.255em;
      }
      .dropstart .dropdown-toggle:before {
        border-bottom: 0.3em solid transparent;
        border-right: 0.3em solid;
        border-top: 0.3em solid transparent;
        content: "";
        display: inline-block;
        margin-right: 0.255em;
        vertical-align: 0.255em;
      }
      .dropstart .dropdown-toggle:empty:after {
        margin-left: 0;
      }
      .dropstart .dropdown-toggle:before {
        vertical-align: 0;
      }
      .dropdown-item {
        background-color: transparent;
        border: 0;
        border-radius: var(--bs-dropdown-item-border-radius, 0);
        clear: both;
        color: var(--bs-dropdown-link-color);
        display: block;
        font-weight: 400;
        padding: var(--bs-dropdown-item-padding-y)
          var(--bs-dropdown-item-padding-x);
        text-align: inherit;
        white-space: nowrap;
        width: 100%;
      }
      .dropdown-item:focus,
      .dropdown-item:hover {
        background-color: var(--bs-dropdown-link-hover-bg);
        background-image: var(--bs-gradient);
        color: var(--bs-dropdown-link-hover-color);
      }
      .dropdown-item.active,
      .dropdown-item:active {
        background-color: var(--bs-dropdown-link-active-bg);
        background-image: var(--bs-gradient);
        color: var(--bs-dropdown-link-active-color);
        text-decoration: none;
      }
      .dropdown-item.disabled,
      .dropdown-item:disabled {
        background-color: transparent;
        background-image: none;
        color: var(--bs-dropdown-link-disabled-color);
        pointer-events: none;
      }
      .dropdown-menu.show {
        display: block;
      }
      .nav-link {
        background: 0 0;
        border: 0;
        color: var(--bs-nav-link-color);
        display: block;
        font-size: var(--bs-nav-link-font-size);
        font-weight: var(--bs-nav-link-font-weight);
        padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
        -webkit-transition: color 0.15s ease-in-out,
          background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
          border-color 0.15s ease-in-out;
      }
      @media (prefers-reduced-motion: reduce) {
        .nav-link {
          -webkit-transition: none;
          transition: none;
        }
      }
      .nav-link:focus,
      .nav-link:hover {
        color: var(--bs-nav-link-hover-color);
      }
      .nav-link:focus-visible {
        -webkit-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        outline: 0;
      }
      .nav-link.disabled,
      .nav-link:disabled {
        color: var(--bs-nav-link-disabled-color);
        cursor: default;
        pointer-events: none;
      }
      .navbar {
        --bs-navbar-padding-x: 0;
        --bs-navbar-padding-y: 0.5rem;
        --bs-navbar-color: rgba(var(--bs-emphasis-color-rgb), 0.65);
        --bs-navbar-hover-color: rgba(var(--bs-emphasis-color-rgb), 0.8);
        --bs-navbar-disabled-color: rgba(var(--bs-emphasis-color-rgb), 0.3);
        --bs-navbar-active-color: rgba(var(--bs-emphasis-color-rgb), 1);
        --bs-navbar-brand-padding-y: 0.3125rem;
        --bs-navbar-brand-margin-end: 1rem;
        --bs-navbar-brand-font-size: 1.25rem;
        --bs-navbar-brand-color: rgba(var(--bs-emphasis-color-rgb), 1);
        --bs-navbar-brand-hover-color: rgba(var(--bs-emphasis-color-rgb), 1);
        --bs-navbar-nav-link-padding-x: 0.5rem;
        --bs-navbar-toggler-padding-y: 0.25rem;
        --bs-navbar-toggler-padding-x: 0.75rem;
        --bs-navbar-toggler-font-size: 1.25rem;
        --bs-navbar-toggler-icon-bg: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 30 30%27%3E%3Cpath stroke=%27rgba%2833, 37, 41, 0.75%29%27 stroke-linecap=%27round%27 stroke-miterlimit=%2710%27 stroke-width=%272%27 d=%27M4 7h22M4 15h22M4 23h22%27/%3E%3C/svg%3E");
        --bs-navbar-toggler-border-color: rgba(
          var(--bs-emphasis-color-rgb),
          0.15
        );
        --bs-navbar-toggler-border-radius: var(--bs-border-radius);
        --bs-navbar-toggler-focus-width: 0.25rem;
        --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
        background-image: var(--bs-gradient);
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x);
        position: relative;
      }
      .navbar,
      .navbar > .container {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
      }
      .navbar > .container {
        -ms-flex-wrap: inherit;
        flex-wrap: inherit;
      }
      .navbar-brand {
        color: var(--bs-navbar-brand-color);
        font-size: var(--bs-navbar-brand-font-size);
        margin-right: var(--bs-navbar-brand-margin-end);
        padding-bottom: var(--bs-navbar-brand-padding-y);
        padding-top: var(--bs-navbar-brand-padding-y);
        white-space: nowrap;
      }
      .navbar-brand:focus,
      .navbar-brand:hover {
        color: var(--bs-navbar-brand-hover-color);
      }
      .navbar-nav {
        --bs-nav-link-padding-x: 0;
        --bs-nav-link-padding-y: 0.5rem;
        --bs-nav-link-font-weight: ;
        --bs-nav-link-color: var(--bs-navbar-color);
        --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
        --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        list-style: none;
        margin-bottom: 0;
        padding-left: 0;
      }
      .navbar-nav .nav-link.active,
      .navbar-nav .nav-link.show {
        color: var(--bs-navbar-active-color);
      }
      .navbar-nav .dropdown-menu {
        position: static;
      }
      .navbar-collapse {
        -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
      }
      .navbar-toggler {
        background-color: transparent;
        border: var(--bs-border-width) solid
          var(--bs-navbar-toggler-border-color);
        border-radius: var(--bs-navbar-toggler-border-radius);
        color: var(--bs-navbar-color);
        font-size: var(--bs-navbar-toggler-font-size);
        line-height: 1;
        padding: var(--bs-navbar-toggler-padding-y)
          var(--bs-navbar-toggler-padding-x);
        -webkit-transition: var(--bs-navbar-toggler-transition);
        transition: var(--bs-navbar-toggler-transition);
      }
      @media (prefers-reduced-motion: reduce) {
        .navbar-toggler {
          -webkit-transition: none;
          transition: none;
        }
      }
      .navbar-toggler:hover {
        text-decoration: none;
      }
      .navbar-toggler:focus {
        -webkit-box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width);
        box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width);
        outline: 0;
        text-decoration: none;
      }
      @media (min-width: 992px) {
        .navbar-expand-lg {
          -ms-flex-wrap: nowrap;
          flex-wrap: nowrap;
          -webkit-box-pack: start;
          -ms-flex-pack: start;
          justify-content: flex-start;
        }
        .navbar-expand-lg .navbar-nav {
          -webkit-box-orient: horizontal;
          -webkit-box-direction: normal;
          -ms-flex-direction: row;
          flex-direction: row;
        }
        .navbar-expand-lg .navbar-nav .dropdown-menu {
          position: absolute;
        }
        .navbar-expand-lg .navbar-nav .nav-link {
          padding-left: var(--bs-navbar-nav-link-padding-x);
          padding-right: var(--bs-navbar-nav-link-padding-x);
        }
        .navbar-expand-lg .navbar-collapse {
          display: -webkit-box !important;
          display: -ms-flexbox !important;
          display: flex !important;
          -ms-flex-preferred-size: auto;
          flex-basis: auto;
        }
        .navbar-expand-lg .navbar-toggler {
          display: none;
        }
        .navbar-expand-lg .offcanvas {
          position: static;
          z-index: auto;
          -webkit-box-flex: 1;
          -ms-flex-positive: 1;
          background-color: transparent !important;
          border: 0 !important;
          -webkit-box-shadow: none;
          box-shadow: none;
          flex-grow: 1;
          height: auto !important;
          -webkit-transform: none !important;
          transform: none !important;
          -webkit-transition: none;
          transition: none;
          visibility: visible !important;
          width: auto !important;
        }
      }
      .alert {
        --bs-alert-bg: transparent;
        --bs-alert-padding-x: 1rem;
        --bs-alert-padding-y: 1rem;
        --bs-alert-margin-bottom: 1rem;
        --bs-alert-color: inherit;
        --bs-alert-border-color: transparent;
        --bs-alert-border: var(--bs-border-width) solid
          var(--bs-alert-border-color);
        --bs-alert-border-radius: var(--bs-border-radius);
        --bs-alert-link-color: inherit;
        background-color: var(--bs-alert-bg);
        border: var(--bs-alert-border);
        border-radius: var(--bs-alert-border-radius);
        color: var(--bs-alert-color);
        margin-bottom: var(--bs-alert-margin-bottom);
        padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
        position: relative;
      }
      .list-group {
        --bs-list-group-color: var(--bs-body-color);
        --bs-list-group-bg: var(--bs-body-bg);
        --bs-list-group-border-color: var(--bs-border-color);
        --bs-list-group-border-width: var(--bs-border-width);
        --bs-list-group-border-radius: var(--bs-border-radius);
        --bs-list-group-item-padding-x: 1rem;
        --bs-list-group-item-padding-y: 0.5rem;
        --bs-list-group-action-color: var(--bs-secondary-color);
        --bs-list-group-action-hover-color: var(--bs-emphasis-color);
        --bs-list-group-action-hover-bg: var(--bs-tertiary-bg);
        --bs-list-group-action-active-color: var(--bs-body-color);
        --bs-list-group-action-active-bg: var(--bs-secondary-bg);
        --bs-list-group-disabled-color: var(--bs-secondary-color);
        --bs-list-group-disabled-bg: var(--bs-body-bg);
        --bs-list-group-active-color: #fff;
        --bs-list-group-active-bg: #0d6efd;
        --bs-list-group-active-border-color: #0d6efd;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        border-radius: var(--bs-list-group-border-radius);
        -ms-flex-direction: column;
        flex-direction: column;
        margin-bottom: 0;
        padding-left: 0;
      }
      .list-group-item {
        background-color: var(--bs-list-group-bg);
        border: var(--bs-list-group-border-width) solid
          var(--bs-list-group-border-color);
        color: var(--bs-list-group-color);
        display: block;
        padding: var(--bs-list-group-item-padding-y)
          var(--bs-list-group-item-padding-x);
        position: relative;
      }
      .list-group-item:first-child {
        border-top-left-radius: inherit;
        border-top-right-radius: inherit;
      }
      .list-group-item:last-child {
        border-bottom-left-radius: inherit;
        border-bottom-right-radius: inherit;
      }
      .list-group-item.disabled,
      .list-group-item:disabled {
        background-color: var(--bs-list-group-disabled-bg);
        color: var(--bs-list-group-disabled-color);
        pointer-events: none;
      }
      .list-group-item.active {
        background-color: var(--bs-list-group-active-bg);
        border-color: var(--bs-list-group-active-border-color);
        color: var(--bs-list-group-active-color);
        z-index: 2;
      }
      .list-group-item + .list-group-item {
        border-top-width: 0;
      }
      .list-group-item + .list-group-item.active {
        border-top-width: var(--bs-list-group-border-width);
        margin-top: calc(var(--bs-list-group-border-width) * -1);
      }
      .toast {
        --bs-toast-zindex: 1090;
        --bs-toast-padding-x: 0.75rem;
        --bs-toast-padding-y: 0.5rem;
        --bs-toast-spacing: 1.5rem;
        --bs-toast-max-width: 350px;
        --bs-toast-font-size: 0.875rem;
        --bs-toast-color: ;
        --bs-toast-bg: rgba(var(--bs-body-bg-rgb), 0.85);
        --bs-toast-border-width: var(--bs-border-width);
        --bs-toast-border-color: var(--bs-border-color-translucent);
        --bs-toast-border-radius: var(--bs-border-radius);
        --bs-toast-box-shadow: var(--bs-box-shadow);
        --bs-toast-header-color: var(--bs-secondary-color);
        --bs-toast-header-bg: rgba(var(--bs-body-bg-rgb), 0.85);
        --bs-toast-header-border-color: var(--bs-border-color-translucent);
        background-clip: padding-box;
        background-color: var(--bs-toast-bg);
        border: var(--bs-toast-border-width) solid var(--bs-toast-border-color);
        border-radius: var(--bs-toast-border-radius);
        -webkit-box-shadow: var(--bs-toast-box-shadow);
        box-shadow: var(--bs-toast-box-shadow);
        color: var(--bs-toast-color);
        font-size: var(--bs-toast-font-size);
        max-width: 100%;
        pointer-events: auto;
        width: var(--bs-toast-max-width);
      }
      .toast.showing {
        opacity: 0;
      }
      .toast:not(.show) {
        display: none;
      }
      .modal {
        --bs-modal-zindex: 1055;
        --bs-modal-width: 500px;
        --bs-modal-padding: 1rem;
        --bs-modal-margin: 0.5rem;
        --bs-modal-color: ;
        --bs-modal-bg: var(--bs-body-bg);
        --bs-modal-border-color: var(--bs-border-color-translucent);
        --bs-modal-border-width: var(--bs-border-width);
        --bs-modal-border-radius: var(--bs-border-radius-lg);
        --bs-modal-box-shadow: var(--bs-box-shadow-sm);
        --bs-modal-inner-border-radius: calc(
          var(--bs-border-radius-lg) - var(--bs-border-width)
        );
        --bs-modal-header-padding-x: 1rem;
        --bs-modal-header-padding-y: 1rem;
        --bs-modal-header-padding: 1rem 1rem;
        --bs-modal-header-border-color: var(--bs-border-color);
        --bs-modal-header-border-width: var(--bs-border-width);
        --bs-modal-title-line-height: 1.5;
        --bs-modal-footer-gap: 0.5rem;
        --bs-modal-footer-bg: ;
        --bs-modal-footer-border-color: var(--bs-border-color);
        --bs-modal-footer-border-width: var(--bs-border-width);
        display: none;
        height: 100%;
        left: 0;
        outline: 0;
        overflow-x: hidden;
        overflow-y: auto;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: var(--bs-modal-zindex);
      }
      .modal-dialog {
        margin: var(--bs-modal-margin);
        pointer-events: none;
        position: relative;
        width: auto;
      }
      .modal.fade .modal-dialog {
        -webkit-transform: translateY(-50px);
        transform: translateY(-50px);
        -webkit-transition: -webkit-transform 0.3s ease-out;
        transition: -webkit-transform 0.3s ease-out;
        transition: transform 0.3s ease-out;
        transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
      }
      @media (prefers-reduced-motion: reduce) {
        .modal.fade .modal-dialog {
          -webkit-transition: none;
          transition: none;
        }
      }
      .modal.show .modal-dialog {
        -webkit-transform: none;
        transform: none;
      }
      .modal.modal-static .modal-dialog {
        -webkit-transform: scale(1.02);
        transform: scale(1.02);
      }
      .modal-backdrop {
        --bs-backdrop-zindex: 1050;
        --bs-backdrop-bg: #000;
        --bs-backdrop-opacity: 0.5;
        background-color: var(--bs-backdrop-bg);
        height: 100vh;
        left: 0;
        position: fixed;
        top: 0;
        width: 100vw;
        z-index: var(--bs-backdrop-zindex);
      }
      .modal-backdrop.fade {
        opacity: 0;
      }
      .modal-backdrop.show {
        opacity: var(--bs-backdrop-opacity);
      }
      .modal-body {
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1 1 auto;
        flex: 1 1 auto;
        padding: var(--bs-modal-padding);
      }
      @media (min-width: 576px) {
        .modal {
          --bs-modal-margin: 1.75rem;
          --bs-modal-box-shadow: var(--bs-box-shadow);
        }
        .modal-dialog {
          margin-left: auto;
          margin-right: auto;
          max-width: var(--bs-modal-width);
        }
      }
      .tooltip {
        --bs-tooltip-zindex: 1080;
        --bs-tooltip-max-width: 200px;
        --bs-tooltip-padding-x: 0.5rem;
        --bs-tooltip-padding-y: 0.25rem;
        --bs-tooltip-margin: ;
        --bs-tooltip-font-size: 0.875rem;
        --bs-tooltip-color: var(--bs-body-bg);
        --bs-tooltip-bg: var(--bs-emphasis-color);
        --bs-tooltip-border-radius: var(--bs-border-radius);
        --bs-tooltip-opacity: 0.9;
        --bs-tooltip-arrow-width: 0.8rem;
        --bs-tooltip-arrow-height: 0.4rem;
        display: block;
        font-family: system-ui, -apple-system, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif;
        font-size: var(--bs-tooltip-font-size);
        font-style: normal;
        font-weight: 400;
        letter-spacing: normal;
        line-break: auto;
        line-height: 1.5;
        margin: var(--bs-tooltip-margin);
        text-align: left;
        text-align: start;
        text-decoration: none;
        text-shadow: none;
        text-transform: none;
        white-space: normal;
        word-break: normal;
        word-spacing: normal;
        z-index: var(--bs-tooltip-zindex);
        word-wrap: break-word;
        opacity: 0;
      }
      .tooltip.show {
        opacity: var(--bs-tooltip-opacity);
      }
      .tooltip .tooltip-arrow {
        display: block;
        height: var(--bs-tooltip-arrow-height);
        width: var(--bs-tooltip-arrow-width);
      }
      .tooltip .tooltip-arrow:before {
        border-color: transparent;
        border-style: solid;
        content: "";
        position: absolute;
      }
      .tooltip-inner {
        background-color: var(--bs-tooltip-bg);
        border-radius: var(--bs-tooltip-border-radius);
        color: var(--bs-tooltip-color);
        max-width: var(--bs-tooltip-max-width);
        padding: var(--bs-tooltip-padding-y) var(--bs-tooltip-padding-x);
        text-align: center;
      }
      .popover {
        --bs-popover-zindex: 1070;
        --bs-popover-max-width: 276px;
        --bs-popover-font-size: 0.875rem;
        --bs-popover-bg: var(--bs-body-bg);
        --bs-popover-border-width: var(--bs-border-width);
        --bs-popover-border-color: var(--bs-border-color-translucent);
        --bs-popover-border-radius: var(--bs-border-radius-lg);
        --bs-popover-inner-border-radius: calc(
          var(--bs-border-radius-lg) - var(--bs-border-width)
        );
        --bs-popover-box-shadow: var(--bs-box-shadow);
        --bs-popover-header-padding-x: 1rem;
        --bs-popover-header-padding-y: 0.5rem;
        --bs-popover-header-font-size: 1rem;
        --bs-popover-header-color: inherit;
        --bs-popover-header-bg: var(--bs-secondary-bg);
        --bs-popover-body-padding-x: 1rem;
        --bs-popover-body-padding-y: 1rem;
        --bs-popover-body-color: var(--bs-body-color);
        --bs-popover-arrow-width: 1rem;
        --bs-popover-arrow-height: 0.5rem;
        --bs-popover-arrow-border: var(--bs-popover-border-color);
        display: block;
        font-family: system-ui, -apple-system, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif;
        font-size: var(--bs-popover-font-size);
        font-style: normal;
        font-weight: 400;
        letter-spacing: normal;
        line-break: auto;
        line-height: 1.5;
        max-width: var(--bs-popover-max-width);
        text-align: left;
        text-align: start;
        text-decoration: none;
        text-shadow: none;
        text-transform: none;
        white-space: normal;
        word-break: normal;
        word-spacing: normal;
        z-index: var(--bs-popover-zindex);
        word-wrap: break-word;
        background-clip: padding-box;
        background-color: var(--bs-popover-bg);
        border: var(--bs-popover-border-width) solid
          var(--bs-popover-border-color);
        border-radius: var(--bs-popover-border-radius);
        -webkit-box-shadow: var(--bs-popover-box-shadow);
        box-shadow: var(--bs-popover-box-shadow);
      }
      .popover .popover-arrow {
        display: block;
        height: var(--bs-popover-arrow-height);
        width: var(--bs-popover-arrow-width);
      }
      .popover .popover-arrow:after,
      .popover .popover-arrow:before {
        border: 0 solid transparent;
        content: "";
        display: block;
        position: absolute;
      }
      .popover-header {
        background-color: var(--bs-popover-header-bg);
        border-bottom: var(--bs-popover-border-width) solid
          var(--bs-popover-border-color);
        border-top-left-radius: var(--bs-popover-inner-border-radius);
        border-top-right-radius: var(--bs-popover-inner-border-radius);
        color: var(--bs-popover-header-color);
        font-size: var(--bs-popover-header-font-size);
        margin-bottom: 0;
        padding: var(--bs-popover-header-padding-y)
          var(--bs-popover-header-padding-x);
      }
      .popover-header:empty {
        display: none;
      }
      .popover-body {
        color: var(--bs-popover-body-color);
        padding: var(--bs-popover-body-padding-y)
          var(--bs-popover-body-padding-x);
      }
      .carousel {
        position: relative;
      }
      .carousel.pointer-event {
        -ms-touch-action: pan-y;
        touch-action: pan-y;
      }
      .carousel-item {
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        display: none;
        float: left;
        margin-right: -100%;
        position: relative;
        -webkit-transition: -webkit-transform 0.6s ease-in-out;
        transition: -webkit-transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out,
          -webkit-transform 0.6s ease-in-out;
        width: 100%;
      }
      @media (prefers-reduced-motion: reduce) {
        .carousel-item {
          -webkit-transition: none;
          transition: none;
        }
      }
      .carousel-item-next,
      .carousel-item-prev,
      .carousel-item.active {
        display: block;
      }
      .active.carousel-item-end,
      .carousel-item-next:not(.carousel-item-start) {
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
      }
      .active.carousel-item-start,
      .carousel-item-prev:not(.carousel-item-end) {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
      }
      .carousel-indicators {
        bottom: 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        left: 0;
        position: absolute;
        right: 0;
        z-index: 2;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-bottom: 1rem;
        margin-left: 15%;
        margin-right: 15%;
        padding: 0;
      }
      .carousel-indicators [data-bs-target] {
        -webkit-box-sizing: content-box;
        box-sizing: content-box;
        -webkit-box-flex: 0;
        background-clip: padding-box;
        background-color: #fff;
        border: 0;
        border-bottom: 10px solid transparent;
        border-top: 10px solid transparent;
        cursor: pointer;
        -ms-flex: 0 1 auto;
        flex: 0 1 auto;
        height: 3px;
        margin-left: 3px;
        margin-right: 3px;
        opacity: 0.5;
        padding: 0;
        text-indent: -999px;
        -webkit-transition: opacity 0.6s;
        transition: opacity 0.6s ease;
        width: 30px;
      }
      @media (prefers-reduced-motion: reduce) {
        .carousel-indicators [data-bs-target] {
          -webkit-transition: none;
          transition: none;
        }
      }
      .carousel-indicators .active {
        opacity: 1;
      }
      .offcanvas {
        --bs-offcanvas-zindex: 1045;
        --bs-offcanvas-width: 400px;
        --bs-offcanvas-height: 30vh;
        --bs-offcanvas-padding-x: 1rem;
        --bs-offcanvas-padding-y: 1rem;
        --bs-offcanvas-color: var(--bs-body-color);
        --bs-offcanvas-bg: var(--bs-body-bg);
        --bs-offcanvas-border-width: var(--bs-border-width);
        --bs-offcanvas-border-color: var(--bs-border-color-translucent);
        --bs-offcanvas-box-shadow: var(--bs-box-shadow-sm);
        --bs-offcanvas-transition: transform 0.3s ease-in-out;
        --bs-offcanvas-title-line-height: 1.5;
      }
      .offcanvas {
        bottom: 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        position: fixed;
        z-index: var(--bs-offcanvas-zindex);
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        background-clip: padding-box;
        background-color: var(--bs-offcanvas-bg);
        -webkit-box-shadow: var(--bs-offcanvas-box-shadow);
        box-shadow: var(--bs-offcanvas-box-shadow);
        color: var(--bs-offcanvas-color);
        -ms-flex-direction: column;
        flex-direction: column;
        max-width: 100%;
        outline: 0;
        -webkit-transition: var(--bs-offcanvas-transition);
        transition: var(--bs-offcanvas-transition);
        visibility: hidden;
      }
      @media (prefers-reduced-motion: reduce) {
        .offcanvas {
          -webkit-transition: none;
          transition: none;
        }
      }
      .offcanvas.show:not(.hiding),
      .offcanvas.showing {
        -webkit-transform: none;
        transform: none;
      }
      .offcanvas.hiding,
      .offcanvas.show,
      .offcanvas.showing {
        visibility: visible;
      }
      .offcanvas-backdrop {
        background-color: #000;
        height: 100vh;
        left: 0;
        position: fixed;
        top: 0;
        width: 100vw;
        z-index: 1040;
      }
      .offcanvas-backdrop.fade {
        opacity: 0;
      }
      .offcanvas-backdrop.show {
        opacity: 0.5;
      }
      .sticky-top {
        top: 0;
      }
      .sticky-top {
        position: sticky;
        z-index: 1020;
      }
      .overflow-hidden {
        overflow: hidden !important;
      }
      .d-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
      }
      .shadow-sm {
        -webkit-box-shadow: var(--bs-box-shadow-sm) !important;
        box-shadow: var(--bs-box-shadow-sm) !important;
      }
      .position-absolute {
        position: absolute !important;
      }
      .top-0 {
        top: 0 !important;
      }
      .start-50 {
        left: 50% !important;
      }
      .border {
        border: var(--bs-border-width) var(--bs-border-style)
          var(--bs-border-color) !important;
      }
      .border-top {
        border-top: var(--bs-border-width) var(--bs-border-style)
          var(--bs-border-color) !important;
      }
      .w-100 {
        width: 100% !important;
      }
      .h-100 {
        height: 100% !important;
      }
      .justify-content-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important;
      }
      .align-items-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important;
      }
      .mt-3 {
        margin-top: 1rem !important;
      }
      .mt-4 {
        margin-top: 1.5rem !important;
      }
      .mt-5 {
        margin-top: 3rem !important;
      }
      .me-3 {
        margin-right: 1rem !important;
      }
      .mb-0 {
        margin-bottom: 0 !important;
      }
      .mb-4 {
        margin-bottom: 1.5rem !important;
      }
      .ms-0 {
        margin-left: 0 !important;
      }
      .ms-auto {
        margin-left: auto !important;
      }
      .p-0 {
        padding: 0 !important;
      }
      .py-3 {
        padding-bottom: 1rem !important;
        padding-top: 1rem !important;
      }
      .pb-2 {
        padding-bottom: 0.5rem !important;
      }
      .pb-4 {
        padding-bottom: 1.5rem !important;
      }
      .ps-0 {
        padding-left: 0 !important;
      }
      .gap-2 {
        gap: 0.5rem !important;
      }
      .gap-3 {
        gap: 1rem !important;
      }
      .gap-4 {
        gap: 1.5rem !important;
      }
      .text-center {
        text-align: center !important;
      }
      .bg-light {
        --bs-bg-opacity: 1;
        background-color: rgba(
          var(--bs-light-rgb),
          var(--bs-bg-opacity)
        ) !important;
      }
      .rounded {
        border-radius: var(--bs-border-radius) !important;
      }
      .visible {
        visibility: visible !important;
      }
      img {
        vertical-align: middle;
      }
      #header .search-form {
        min-width: 250px;
      }
      .navbar-brand img {
        height: 40px;
      }
      #main {
        margin-top: 0;
      }
      #footer {
        background-color: #fff;
      }
      #footer p {
        margin-top: 10px;
      }
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Phudu;
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Phudu;
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      :root {
        --body-bg-color: #fff;
        --body-text-color: #333;
        --theme-light-color: #fff;
        --theme-dark-color: #c41421;
        --theme-main-color: #d5232b;
        --theme-box-hover-color: rgba(82, 164, 206, 0.43);
        --theme-heading-font: Phudu;
        --theme-text-font: Inter;
      }
      body,
      html {
        overflow: visible !important;
        height: auto !important;
      }
      body {
        font-family: var(--theme-text-font), sans-serif;
        font-size: 18px;
        line-height: 28px;
        font-weight: 400;
        letter-spacing: -0.24px;
        color: var(--body-text-color);
        background-color: var(--body-bg-color);
      }
      .row {
        margin-left: 0 !important;
        margin-right: 0 !important;
      }
      .desktop {
        display: block;
      }
      .tablet {
        display: none;
      }
      a.btn.btn-light.me-3.tablet.ms-auto {
        display: none !important;
      }
      .mobile {
        display: none;
      }
      h1,
      h2,
      h3 {
        font-family: var(--theme-heading-font), sans-serif;
        font-style: normal;
      }
      h1 {
        color: #333;
        font-size: 48px;
        font-weight: 700;
        line-height: 60px;
        letter-spacing: -0.16px;
        text-transform: uppercase;
      }
      h2 {
        color: #333;
        font-size: 48px;
        font-weight: 600;
        line-height: 60px;
        letter-spacing: -0.32px;
        padding-bottom: 1rem;
        text-transform: uppercase;
      }
      p {
        margin: 0;
      }
      a {
        color: var(--theme-main-color);
      }
      #main {
        margin: 0 !important;
      }
      .rounded {
        border-radius: 16px !important;
      }
      .zindex9 {
        z-index: 9;
      }
      .menu-title {
        color: #333;
        font-family: var(--theme-heading-font), sans-serif;
        font-size: 20px;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 28px;
        letter-spacing: -0.176px;
        padding: 15px 20px 0;
      }
      .header {
        position: sticky;
        top: 0;
        z-index: 1111;
      }
      .navbar {
        background-color: var(--theme-light-color) !important;
        padding: 1rem;
      }
      .navbar-brand {
        margin-right: 0 !important;
        padding-bottom: 0 !important;
        padding-top: 0 !important;
        white-space: nowrap;
      }
      .navbar-brand img {
        height: 65px !important;
        min-width: 221px;
      }
      .nav-item {
        margin-left: 12px;
        margin-right: 12px;
      }
      .nav-link {
        color: #333 !important;
        padding: 15px 8px !important;
        font-family: var(--theme-text-font), sans-serif !important;
        font-size: 16px;
        font-weight: 500 !important;
        text-transform: none;
        text-decoration: none;
        line-height: 24px;
      }
      .nav-link:hover {
        color: var(--theme-main-color) !important;
        text-decoration: underline;
        text-decoration-color: var(--theme-main-color) !important;
        text-underline-offset: 8px;
        text-decoration-thickness: 2px;
      }
      .navbar-nav .active .nav-link {
        color: var(--theme-main-color) !important;
        text-decoration: underline;
        text-decoration-color: var(--theme-main-color) !important;
        text-underline-offset: 8px;
        text-decoration-thickness: 2px;
      }
      .dropdown-menu-compressed {
        width: 180px !important;
      }
      .dropdown-menu {
        background-color: var(--theme-light-color);
        color: #333 !important;
        border-radius: 0;
        border: 1px solid #bbb;
        position: absolute;
        z-index: 9999;
        width: 820px;
        text-align: left;
        list-style: none;
        margin-top: 15px !important;
        padding: 0 !important;
        overflow-y: auto;
      }
      .dropdown-menu::-webkit-scrollbar {
        width: 6px;
        background: var(--theme-light-color) !important;
      }
      .dropdown-menu::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
      }
      .dropdown-menu::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
      svg.menu-arrow {
        width: 7px !important;
        margin-right: 6px !important;
        align-content: center;
        display: inline-flex;
        vertical-align: text-bottom;
      }
      .dropdown-item {
        font-family: var(--theme-text-font), sans-serif;
        font-size: 16px !important;
        font-weight: 500 !important;
        text-transform: none;
        text-decoration: none;
        line-height: 24px;
        letter-spacing: -0.176px;
        padding: 15px 20px;
        background-color: var(--theme-light-color);
      }
      .dropdown-item.active,
      .dropdown-item:active {
        background-color: transparent !important;
        background-image: var(--bs-gradient);
        color: var(--theme-main-color) !important;
        text-decoration: none;
      }
      .dropdown-item:focus,
      .dropdown-item:hover {
        color: var(--theme-main-color) !important;
        background-color: var(--theme-light-color) !important;
      }
      .navbar-toggler {
        background-color: #333;
        color: var(--theme-light-color) !important;
        width: 50px;
        padding: 5px 40px 5px 10px;
        border-radius: 25px;
        background-image: none !important;
      }
      .navbar-toggler svg {
        color: #fff !important;
        width: 30px !important;
        height: 30px !important;
      }
      .dropdown-toggle:after {
        position: absolute;
        margin-top: 10px;
      }
      svg#Layer_1 {
        margin-right: 6px;
      }
      .footer-cta {
        margin-bottom: -215px;
        position: relative;
        z-index: 1;
      }
      .footer-cta .footer-dark {
        background-color: var(--theme-dark-color);
        padding: 5.5rem 5rem;
        border-radius: 50px 0 0 50px;
        width: 70%;
      }
      .footer-cta .footer-light {
        background-color: var(--theme-main-color);
        align-content: center;
        border-radius: 0 50px 50px 0;
        width: 30%;
      }
      .footer-cta .footer-quote {
        color: #fff;
        font-family: var(--theme-heading-font), sans-serif;
        font-size: 30px;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 38px;
      }
      .footer-cta .review-image {
        margin-right: 25px;
        width: 20%;
        height: auto;
      }
      .footer-cta .review-title {
        color: #fff;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: -0.176px;
        align-content: center;
      }
      .btn-light.quote-button-footer {
        border: 2px solid #fff;
        background-color: #fff !important;
        color: #333;
        padding: 0 10px 0 0;
        background-image: none !important;
        --bs-btn-box-shadow: none !important;
      }
      .btn-light.quote-button-footer:hover {
        border: 2px solid var(--theme-main-color);
        background-color: var(--theme-main-color);
        color: #fff;
      }
      .footer-light-section {
        background-color: var(--theme-light-color) !important;
        min-height: 250px;
        position: relative;
        z-index: 0;
      }
      .footer {
        background-color: var(--theme-light-color) !important;
        padding: 5rem 5rem 2rem;
      }
      .footer .footer-logo {
        width: 35%;
        height: auto;
      }
      .footer .footer-title {
        color: #666;
        font-family: var(--theme-text-font), Sans-serif;
        font-size: 20px;
        font-weight: 500;
        text-transform: uppercase;
        text-decoration: none;
        line-height: 32px;
        letter-spacing: -0.24px;
        padding-bottom: 1rem;
      }
      .footer .footer-link {
        display: block;
        padding: 5px 0;
        color: #333 !important;
        font-family: var(--theme-text-font), sans-serif;
        font-size: 16px !important;
        font-weight: 500 !important;
        text-transform: none;
        text-decoration: none;
        line-height: 24px;
        letter-spacing: -0.176px;
      }
      .footer .footer-link:hover {
        color: var(--theme-main-color) !important;
      }
      .footer .border-top {
        border-top: 1px solid var(--theme-main-color) !important;
      }
      .footer .copyright-title {
        color: #666;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: -0.176px;
        text-align: center;
      }
      .footer .btn-light {
        padding: 10px 10px 10px 1px !important;
      }
      .footer-sticky {
        display: none;
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        width: 100%;
        z-index: 10;
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
        padding: 10px 0;
        background-color: var(--theme-light-color);
        text-align: center;
        transform: translateY(100%);
        transition: transform 0.3s ease-in-out;
      }
      .footer-sticky a {
        font-size: 16px;
        line-height: 14px;
        color: #fff !important;
      }
      .footer-sticky .call-svg {
        margin-right: 6px !important;
        margin-bottom: 0 !important;
      }
      .footer-sticky .btn-light {
        padding: 7px 4px 7px 0 !important;
        margin-bottom: 0 !important;
      }
      .footer-sticky .btn-dark {
        padding: 2px 6px 2px 1px !important;
        margin-bottom: 0 !important;
      }
      .footer-sticky .border-right {
        border-right: 2px solid var(--theme-main-color);
      }
      .dark-footer {
        background: #040404;
      }
      .dark-footer img {
        width: 30%;
        margin: auto;
      }
      .btn {
        padding: 7px 16px;
        border-radius: 50px;
        font-family: var(--theme-text-font), sans-serif;
        font-weight: 500;
        font-size: 18px;
        line-height: 24px;
        letter-spacing: -0.24px;
        text-decoration: none;
      }
      .btn-light {
        border: 2px solid var(--theme-main-color);
        padding: 9.5px 10px 9.5px 12px;
        background-color: var(--theme-main-color) !important;
        color: #fff;
        background-image: none !important;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        --bs-btn-box-shadow: none !important;
      }
      .btn-light:hover {
        border: 2px solid var(--theme-main-color);
        background-color: var(--theme-main-color) !important;
        color: #fff;
      }
      .btn-dark {
        border: 2px solid #333;
        padding: 3px 10px 2px 3px;
        border-radius: 50px;
        background-image: none !important;
        background-color: #333;
        color: #fff;
        --bs-btn-box-shadow: none !important;
      }
      .btn-dark:hover {
        border: 2px solid var(--theme-main-color);
        background-color: var(--theme-main-color);
        color: #fff;
      }
      .header-button .btn-light {
        border: 2px solid var(--theme-main-color);
        padding: 7px 10px 7px 1px;
        background-color: var(--theme-main-color) !important;
        color: #fff;
        background-image: none !important;
        --bs-btn-box-shadow: none !important;
        align-content: center !important;
      }
      .header-button .btn-light:hover {
        border: 2px solid var(--theme-main-color);
        background-color: var(--theme-main-color) !important;
        color: #fff;
      }
      .header-button .btn-dark {
        border: 2px solid #333;
        padding: 3px 10px 2px 3px;
        border-radius: 50px;
        background-image: none !important;
        background-color: #333;
        color: #fff;
        --bs-btn-box-shadow: none !important;
      }
      .header-button .btn-dark:hover {
        border: 2px solid var(--theme-main-color);
        background-color: var(--theme-main-color);
        color: #fff;
      }
      .btn-link {
        color: var(--theme-main-color) !important;
        text-align: start;
        font-size: 14px;
      }
      .call-svg {
        margin-right: 10px;
        margin-bottom: 1px;
      }
      .e-fas-arrow-right {
        margin-left: 10px;
        margin-bottom: 2px;
      }
      .header-button {
        height: 100%;
      }
      .bus-buttons .btn-light {
        padding: 13px 16px !important;
      }
      div#accordion {
        width: 60%;
        margin: auto;
      }
      .collapse,
      .collapsing {
        transition: none !important;
      }
      .faq {
        border-top: 1px solid #ddd;
        padding: 15px 0;
      }
      .faq a {
        color: #333;
      }
      .faq-body {
        padding-top: 10px;
        font-size: 16px;
      }
      .faq-main-title {
        text-align: center;
        padding-bottom: 3rem;
      }
      .faq-section {
        padding: 0 0 5rem;
      }
      .faq-header {
        font-weight: 600;
        font-size: 18px !important;
      }
      .action-cta {
        justify-items: center;
        text-align: center;
      }
      .btn-light.quote-button {
        border: 2px solid #fff !important;
        background-color: #fff !important;
        padding: 0 10px 0 0 !important;
        color: #333;
        background-image: none !important;
        --bs-btn-box-shadow: none !important;
      }
      .btn-light.quote-button:hover {
        border: 2px solid var(--theme-main-color) !important;
        background-color: var(--theme-main-color) !important;
        color: #fff;
      }
      .btn-dark.call-button {
        border: 2px solid #fff;
        background-image: none !important;
        background-color: #fff;
        color: #333;
        --bs-btn-box-shadow: none !important;
      }
      .btn-dark.call-button:hover {
        border: 2px solid var(--theme-main-color);
        background-color: var(--theme-main-color);
        color: #fff;
      }
      .light-cta-section {
        padding-top: 4rem;
      }
      .light-cta-section p.heading {
        color: #333;
        font-family: var(--theme-heading-font), sans-serif;
        font-size: 24px;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 32px;
        letter-spacing: -0.176px;
        border-top: 1px solid #eee;
        padding: 75px 0 20px;
        text-align: center;
        margin: 0;
      }
      .light-cta-section p {
        font-weight: 500;
        font-size: 18px;
        text-align: center;
      }
      .client-title {
        color: #333;
        font-size: 24px;
        font-family: var(--theme-heading-font), sans-serif;
        font-weight: 600;
        line-height: 32px;
        letter-spacing: -0.176px;
        text-transform: uppercase;
      }
      .client-title p {
        color: #333;
        font-size: 48px;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 56px;
        letter-spacing: -0.32px;
        padding-bottom: 48px;
        padding-top: 48px;
        width: 65%;
        text-align: center;
        margin: auto;
      }
      .clients-logos {
        padding: 4rem 3rem 0;
      }
      .clients-logos {
        text-align: center;
      }
      .client-section {
        padding: 40px 0 58px;
      }
      .client-section .client-title {
        padding-top: 40px;
      }
      .testimonial .repeater-item {
        background: #fff;
        padding: 20px 20px 40px;
        border-radius: 16px;
        height: 315px;
        box-shadow: 1px 1px 7px 0 rgba(0, 0, 0, 0.13),
          0 0 2px 0 rgba(0, 0, 0, 0.05);
        margin-right: 5px;
      }
      .testimonial .repeater-item img {
        border-radius: 50%;
      }
      .testimonial .rating-container {
        padding: 10px 0;
      }
      .testimonial .rating-container .testimonial-star {
        color: #f6bb06;
        font-size: 25px;
      }
      .testimonial-message {
        font-size: 16px;
        font-family: var(--theme-text-font), sans-serif;
      }
      .testimonial-container:hover {
        transform: translate(0, -5px);
        transition: transform 0.3s ease-out;
      }
      .image-banner {
        background-color: var(--theme-light-color);
        background-position: top right;
        background-repeat: no-repeat;
        background-size: 50% auto;
        padding: 0.5rem 5rem 7.5rem;
      }
      .image-banner-home {
        background-color: var(--theme-light-color);
        background-position: top right;
        background-repeat: no-repeat;
        background-size: 50% auto;
        padding: 0.5rem 5rem 9rem;
        position: relative;
        z-index: 0;
      }
      .banner-image-home {
        padding-top: 4rem;
        padding-bottom: 4rem;
      }
      .hero-banner {
        --bs-gutter-x: 50px;
      }
      .hero-banner img {
        width: 100%;
      }
      .hero-title {
        padding-bottom: 1rem;
      }
      .banner-button {
        padding-top: 22px;
      }
      .light-banner {
        background-color: #fafafa;
        background-image: url(https://busrentalcompanyclifton.com/wp-content/themes/charter-bus/images/background-pattern.png);
        background-position: top center;
        background-repeat: no-repeat;
        background-size: cover;
        padding-top: 6rem;
        padding-bottom: 6rem;
      }
      .light-banner-home {
        background-color: #fafafa;
        background-image: url(https://busrentalcompanyclifton.com/wp-content/themes/charter-bus/images/background-pattern.png);
        background-position: top center;
        background-repeat: no-repeat;
        background-size: cover;
        padding-top: 6rem;
        padding-bottom: 15rem;
      }
      .light-banner-section-home {
        --bs-gutter-x: 5rem;
      }
      .light-banner-section-home p,
      h2 {
        width: 100%;
      }
      .page-template-home .homepage-cta {
        background-color: var(--theme-dark-color);
        background-image: url(https://busrentalcompanyclifton.com/wp-content/themes/charter-bus/images/home-cta-bg.png);
        background-repeat: no-repeat;
        background-size: cover;
        padding: 5rem 10rem;
      }
      .page-template-home .homepage-cta h2 {
        color: #fff;
        width: 50%;
        margin: auto;
        text-align: center;
        margin-bottom: 30px;
      }
      .col-xxl-3.col-xl-3.col-lg-4.col-md-6.col-sm-12.col-xs-12.amenities-card-section {
        margin-bottom: 26px;
      }
      .page-template-home .hero-box-banner {
        background-color: #fff;
        box-shadow: 0 4px 10px -2px rgba(0, 0, 0, 0.5);
        padding: 20px 40px;
        margin: 0 6px;
        border-radius: 16px;
        box-sizing: content-box;
        height: 80%;
      }
      .hero-box-banner:hover {
        box-shadow: 0 4px 10px -2px var(--theme-box-hover-color);
      }
      .home-hero-banner-button {
        padding: 0;
      }
      .page-template-home .hero-box p {
        color: #333;
        font-family: var(--theme-heading-font), sans-serif;
        font-size: 24px;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 32px;
        letter-spacing: -0.176px;
        padding-left: 25px;
        padding-bottom: 0;
      }
      .page-template-home .hero-box {
        margin-top: -85px;
        position: relative;
        z-index: 1;
      }
      .page-template-home .home-service {
        padding-top: 6rem;
      }
      .page-template-home .home-service h2 {
        width: 72%;
        text-align: center;
        margin: auto;
        padding-bottom: 3rem;
      }
      .page-template-home .service-card h2 {
        color: #333;
        font-family: var(--theme-heading-font), sans-serif;
        font-size: 24px;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 32px;
        letter-spacing: -0.176px;
        padding-bottom: 12px;
        padding-top: 1rem;
        margin: 0;
        width: 100%;
        text-align: start;
      }
      .page-template-home .service-card h2 a {
        color: #333;
      }
      .page-template-home .service-card h2 a:hover {
        color: #333;
      }
      .page-template-home .service-card p {
        padding-top: 10px;
      }
      .page-template-home .service-card {
        padding: 0 40px;
      }
      .price-card {
        position: absolute;
        background: #fff;
        z-index: 1;
        padding: 5px 15px;
        margin-left: 10px;
        border-radius: 50px;
        font-size: 14px;
        translate: 0px -20px;
      }
      .buses-home h2 {
        width: 45%;
        margin: auto;
        text-align: center;
      }
      .buses-home {
        padding: 8rem 0 6rem;
      }
      .view-bus-button {
        padding: 13px 16px !important;
        border-radius: 50px;
        font-family: var(--theme-text-font), sans-serif;
        font-weight: 500;
        font-size: 18px;
        text-decoration: none;
        line-height: 24px;
        letter-spacing: -0.24px;
      }
      .bus-buttons {
        padding-bottom: 5px;
      }
      .bus-card {
        padding: 25px;
      }
      .buses-cards {
        border: 1px solid #e0e0e0;
        padding: 3rem 2rem 1.5rem;
        border-radius: 32px;
        background-color: #fafafa;
        min-height: 100%;
      }
      .buses-cards h2,
      .buses-cards h3 {
        color: #333;
        font-family: var(--theme-heading-font), sans-serif;
        font-size: 24px;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 32px;
        letter-spacing: -0.176px;
        padding: 32px 0 25px;
        text-align: center;
      }
      .buses-cards h2 a,
      .buses-cards h3 a {
        color: #333;
      }
      .buses-cards h3 a:hover {
        color: #333;
      }
      .sub-buses-2 {
        padding-left: 60px;
      }
      .amenities-card {
        text-align: center;
        padding: 24px 16px;
        border: 1px solid #e0e0e0;
        align-content: center;
        margin: 0 8px;
        border-radius: 16px;
        min-height: 180px;
        box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
      }
      .amenities {
        padding-bottom: 58px;
        padding-top: 58px;
      }
      .amenities .amenities-title {
        font-size: 24px;
        line-height: 32px;
        font-weight: 600;
        font-family: var(--theme-heading-font), sans-serif;
        text-align: center;
        padding-bottom: 20px;
        width: 100% !important;
      }
      .amenities-card p {
        padding: 20px 0 0;
        margin: 0;
        font-weight: 500;
        text-align: center;
        margin: auto;
      }
      .amenities-title h2 {
        color: #333;
        font-family: var(--theme-heading-font), sans-serif;
        font-size: 24px;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 32px;
        letter-spacing: -0.176px;
        padding: 58px 0 48px;
        text-align: center;
        margin: 0 auto;
        border-top: 1px solid #eee;
        width: 65%;
      }
      .light-border {
        color: #eee;
        width: 60%;
        margin: auto;
        opacity: 1;
      }
      .page-template-home .price-table {
        padding: 0 160px 52px;
      }
      .page-template-home .prices {
        padding: 5rem 0;
      }
      .page-template-home .prices p {
        width: 61%;
        font-size: 16px;
        text-align: center;
        padding-bottom: 34.4px;
        margin: auto;
      }
      table {
        margin: 0 !important;
      }
      th {
        padding: 20px 26px !important;
        background-color: var(--theme-light-color) !important;
        border-style: solid;
        border-color: #fff;
        border-width: 8px;
        align-content: center;
      }
      td {
        padding: 26px !important;
        background-color: #fafafa !important;
        border-style: solid;
        border-color: #fff;
        border-width: 8px;
        font-size: 18px;
        color: #333 !important;
        align-content: center;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: -0.24px;
      }
      .table-title th {
        color: #333 !important;
        font-size: 24px;
        font-family: var(--theme-heading-font), sans-serif;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 32px;
        letter-spacing: -0.176px;
      }
      @media (min-width: 1900px) {
        .container {
          max-width: 1440px !important;
          padding: 0;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
        }
      }
      @media (min-width: 1600px) and (max-width: 1899px) {
        .container {
          max-width: 1440px !important;
          padding: 0;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
        }
      }
      @media (min-width: 1441px) and (max-width: 1599px) {
        .container {
          max-width: 1440px !important;
          padding: 0;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
        }
        .header-button .btn-dark {
          font-size: 16px !important;
        }
        .header-button .btn-light {
          font-size: 16px !important;
        }
      }
      @media (min-width: 1400px) and (max-width: 1440px) {
        .container {
          max-width: 1300px !important;
          padding: 0;
        }
        h1 {
          font-size: 48px;
          line-height: 46px;
        }
        h2 {
          font-size: 48px;
          line-height: 46px;
        }
        h3 {
          font-size: 22px;
          line-height: 25px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
        }
        .navbar-brand img {
          height: 65px !important;
          min-width: 188px;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
        }
        .dropdown-item {
          font-size: 14px !important;
          line-height: 20px;
        }
        .nav-link {
          font-size: 14px;
          line-height: 20px;
        }
        .footer {
          background-color: var(--theme-light-color) !important;
          padding: 5rem 0 2rem;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
        }
        .dark-footer img {
          width: 30%;
          margin: auto;
        }
        .btn {
          font-size: 18px;
        }
        .header-button .btn-light {
          font-size: 16px !important;
        }
        .header-button .btn-dark {
          font-size: 16px !important;
        }
        .light-cta-section {
          padding-top: 4rem;
        }
        .light-cta-section p.heading {
          font-size: 22px;
          line-height: 25px;
        }
        .light-cta-section p {
          font-size: 18px;
          font-weight: 500;
          text-align: center;
          width: 100%;
        }
        .client-title {
          font-size: 22px;
          line-height: 25px;
        }
        .clients-logos {
          padding: 5rem 3rem 2.5rem;
        }
        .testimonial p {
          font-size: 14px;
        }
        .testimonial .repeater-item img {
          width: 20%;
        }
        .light-banner {
          padding: 6rem 3rem;
        }
        .image-banner-home {
          background-image: none;
          padding: 0 3rem 7.3rem !important;
        }
        .home-hero-banner-button {
          padding: 0 !important;
        }
        .image-banner-home {
          background-position: top right;
          background-size: 46% auto;
        }
        .page-template-home .hero-box {
          padding: 0 2.5rem;
          margin-top: -66px;
        }
        .page-template-home .hero-boxes {
          padding: 0 10px;
        }
        .page-template-home .hero-box p {
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .service-card h2 a {
          font-size: 22px;
        }
        .page-template-home .light-banner-home {
          padding: 6rem 3rem 10rem;
        }
        .page-template-home .light-banner-section-home h2,
        p {
          width: 100%;
        }
        .page-template-home .buses-home {
          padding: 5.5rem 2.2rem 5rem;
        }
        .page-template-home .buses-cards {
          background-color: #fafafa;
          padding: 2.5rem 1rem 1rem;
        }
        .buses-home h2 {
          width: 100%;
          padding-bottom: 35px;
        }
        .page-template-home .bus-card {
          padding: 16px 16px 20px;
        }
        .page-template-home .buses-cards h3 {
          font-size: 22px;
          line-height: 28px;
        }
        .page-template-home .homepage-cta h2 {
          width: 100% !important;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 15rem 6rem;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 28px;
        }
        .amenities .amenities-title {
          font-size: 22px;
          line-height: 25px;
          padding-bottom: 22px;
        }
        h2.desktop {
          font-size: 28px;
          line-height: 10px;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .price-table {
          padding: 0 0 38px;
        }
        .page-template-home .prices {
          padding: 5rem 0.5rem;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 16px;
          text-align: center;
          padding-bottom: 34.4px;
          margin: auto;
        }
        .table-title th {
          font-size: 22px;
          line-height: 25px;
        }
        .sub-buses-2 {
          padding-left: 40px;
        }
        .sub-buses-2 {
          padding-left: 60px;
        }
      }
      @media (min-width: 1361px) and (max-width: 1399px) {
        .container {
          max-width: 1300px !important;
          padding: 0;
        }
        h1 {
          font-size: 48px;
          line-height: 46px;
        }
        h2 {
          font-size: 48px;
          line-height: 46px;
          width: 100% !important;
        }
        p {
          font-size: 16px;
          line-height: 28px;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
          min-width: 180px;
        }
        .navbar-brand img {
          height: 65px !important;
        }
        .dropdown-item {
          font-size: 14px !important;
          line-height: 20px;
        }
        .nav-link {
          font-size: 14px;
          line-height: 20px;
        }
        .nav-item {
          margin-left: 5px;
          margin-right: 5px;
        }
        .footer-cta {
          padding-left: 3rem;
          padding-right: 3rem;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
          width: 100% !important;
        }
        .footer {
          background-color: var(--theme-light-color) !important;
          padding: 5rem 0 2rem;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .dark-footer img {
          width: 40%;
          margin: auto;
        }
        .btn {
          font-size: 18px;
        }
        .light-cta-section {
          padding-top: 1rem;
        }
        .light-cta-section p.heading {
          font-size: 22px;
          line-height: 25px;
        }
        .light-cta-section p {
          font-size: 18px;
          font-weight: 500;
          text-align: center;
          width: 100%;
        }
        .client-title {
          font-size: 22px;
          line-height: 25px;
          width: 100%;
        }
        .clients-logos {
          padding: 5rem 0 2.5rem;
        }
        .testimonial p {
          font-size: 14px;
        }
        .testimonial .repeater-item img {
          width: 20%;
        }
        .testimonial .repeater-item {
          min-height: 360px !important;
        }
        .image-banner {
          padding: 0.5rem 3rem 3.5rem;
        }
        .light-banner {
          padding: 6rem 3.5rem;
        }
        .image-banner-home {
          background-image: none;
          padding: 0 3rem 8.3rem !important;
        }
        .home-hero-banner-button {
          padding: 0 !important;
        }
        .image-banner-home {
          background-position: top right;
          background-size: 46% auto;
        }
        .hero-box {
          padding: 0 2.5rem;
          margin-top: -78px;
        }
        .hero-boxes {
          padding: 0 10px;
        }
        .hero-box p {
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .service-card h2 a {
          font-size: 22px;
        }
        .page-template-home .light-banner-home {
          padding: 6rem 3rem 10rem;
        }
        .page-template-home .light-banner-section-home h2,
        p {
          width: 90%;
        }
        .page-template-home .buses-home {
          padding: 5.5rem 2.2rem 5rem;
        }
        .page-template-home .buses-cards {
          background-color: #fafafa;
          padding: 2.5rem 1rem 1rem;
        }
        .buses-home h2 {
          width: 100%;
          padding-bottom: 35px;
        }
        .page-template-home .bus-card {
          padding: 16px 16px 20px;
        }
        .page-template-home .buses-cards h3 {
          font-size: 22px;
          line-height: 28px;
        }
        .page-template-home .homepage-cta h2 {
          width: 100% !important;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 15rem 6rem;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 28px;
        }
        .amenities .amenities-title {
          font-size: 22px;
          line-height: 25px;
          padding-bottom: 22px;
          width: 100% !important;
        }
        h2.desktop {
          font-size: 28px;
          line-height: 10px;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .price-table {
          padding: 0 0 38px;
        }
        .page-template-home .prices {
          padding: 5rem 1.8rem;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 16px;
          text-align: center;
          padding-bottom: 34.4px;
          margin: auto;
        }
        .table-title th {
          font-size: 22px;
          line-height: 25px;
        }
        .sub-buses-2 {
          padding-left: 40px;
        }
        .sub-buses-2 {
          padding-left: 60px;
        }
      }
      @media (min-width: 1281px) and (max-width: 1360px) {
        .container {
          max-width: 1200px !important;
          padding: 0;
        }
        h1 {
          font-size: 48px;
          line-height: 46px;
        }
        h2 {
          font-size: 48px;
          line-height: 46px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
          min-width: 153px;
        }
        .navbar-brand img {
          height: 55px !important;
          min-width: 175px;
        }
        .nav-item {
          margin-left: 5px;
          margin-right: 5px;
        }
        .dropdown-item {
          font-size: 14px !important;
          line-height: 20px;
        }
        .nav-link {
          font-size: 14px;
          line-height: 20px;
        }
        .footer-cta {
          padding-left: 3rem;
          padding-right: 3rem;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
        }
        .footer {
          background-color: var(--theme-light-color) !important;
          padding: 5rem 0 2rem;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .dark-footer img {
          width: 40%;
          margin: auto;
        }
        .btn {
          font-size: 18px;
        }
        .header-button .btn-light {
          font-size: 16px !important;
        }
        .header-button .btn-dark {
          font-size: 16px !important;
        }
        .view-bus-button {
          padding: 13px !important;
        }
        .banner-button .btn-dark {
          margin-bottom: 20px !important;
        }
        .light-cta-section {
          padding-top: 1rem;
        }
        .light-cta-section p.heading {
          font-size: 22px;
          line-height: 25px;
        }
        .light-cta-section p {
          font-size: 18px;
          font-weight: 500;
        }
        .client-title {
          font-size: 22px;
          line-height: 25px;
        }
        .clients-logos {
          padding: 5rem 0 2.5rem;
        }
        .testimonial p {
          font-size: 14px;
        }
        .testimonial .repeater-item img {
          width: 20%;
        }
        .testimonial .repeater-item {
          min-height: 380px !important;
        }
        .image-banner {
          padding: 0.5rem 3rem 3.5rem;
        }
        .light-banner {
          padding: 5rem 3rem;
        }

        .image-banner-home {
          background-image: none;
          padding: 0 0 9.3rem 2.5rem !important;
        }
        .home-hero-banner-button {
          padding: 0 !important;
        }
        .image-banner-home {
          background-position: top right;
          background-size: 46% auto;
        }
        .hero-box {
          padding: 0 2.5rem;
          margin-top: -80px;
        }
        .hero-boxes {
          padding: 0 10px;
        }
        .hero-box p {
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .buses-home {
          padding: 5.5rem 2.2rem 5rem 2.2em;
        }
        .page-template-home .buses-cards {
          background-color: #fafafa;
          padding: 2.5rem 1rem 1rem;
        }
        .buses-home h2 {
          width: 100%;
          padding-bottom: 35px;
        }
        .page-template-home .bus-card {
          padding: 16px 16px 44px;
        }
        .homepage-cta h2 {
          width: 100% !important;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 15rem 6rem;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 28px;
        }
        .amenities .amenities-title {
          font-size: 22px;
          line-height: 25px;
          padding-bottom: 22px;
        }
        h2.desktop {
          font-size: 28px;
          line-height: 10px;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .price-table {
          padding: 0 0 38px;
        }
        .page-template-home .prices {
          padding: 5rem 1.2rem;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 16px;
          text-align: center;
          padding-bottom: 34.4px;
          margin: auto;
        }
        .table-title th {
          font-size: 22px;
          line-height: 25px;
        }
      }
      @media (min-width: 1221px) and (max-width: 1280px) {
        .container {
          max-width: 1200px !important;
          padding: 0;
        }
        h1 {
          font-size: 48px;
          line-height: 46px;
        }
        h2 {
          font-size: 50px;
          line-height: 46px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
          min-width: 153px;
        }
        .navbar-brand img {
          height: 55px !important;
          min-width: 175px;
        }
        .nav-item {
          margin-left: 3px;
          margin-right: 3px;
        }
        .dropdown-item {
          font-size: 14px !important;
          line-height: 20px;
        }
        .nav-link {
          font-size: 14px;
          line-height: 20px;
        }
        .e-fas-arrow-right {
          margin-left: 10px;
          margin-bottom: 2px;
          width: 14px;
          height: 14px;
        }
        .footer {
          padding: 5rem 3.5rem 2rem;
        }
        .footer-cta .footer-dark {
          padding: 4.5rem 3rem;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
        }
        .footer {
          background-color: var(--theme-light-color) !important;
          padding: 5rem 0 2rem;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .dark-footer img {
          width: 40%;
          margin: auto;
        }
        .btn {
          font-size: 18px;
        }
        .banner-button .btn-dark {
          margin-bottom: 20px;
        }
        .hero-banner .banner-button .btn-dark {
          padding: 4px 70px 2px 3px !important;
        }
        .hero-banner .banner-button .call-svg {
          margin-right: 30px !important;
          margin-bottom: 1px;
        }
        .hero-banner .banner-button .btn-light {
          padding: 9.5px 39px !important;
        }
        .header-button .btn-dark {
          font-size: 16px !important;
        }
        .header-button .btn-light {
          font-size: 16px !important;
        }
        .banner-button .btn-dark {
          margin-right: 0 !important;
        }
        .light-cta-section {
          padding-top: 3rem;
        }
        .light-cta-section p.heading {
          font-size: 22px;
          line-height: 25px;
        }
        .light-cta-section p {
          font-size: 18px;
          font-weight: 500;
        }
        .client-title {
          font-size: 22px;
          line-height: 25px;
        }
        .clients-logos {
          padding: 4rem 0 2rem;
        }
        .testimonial p {
          font-size: 14px;
        }
        .testimonial .repeater-item img {
          width: 20%;
        }
        .testimonial .repeater-item {
          min-height: 380px !important;
        }
        .image-banner {
          padding: 0.5rem 3rem 3.5rem;
        }
        .light-banner {
          padding: 6rem 3rem;
        }
        .image-banner-home {
          background-image: none;
          padding: 0 3rem 9.3rem !important;
        }
        .home-hero-banner-button {
          padding: 0 !important;
        }
        .image-banner-home {
          background-position: top right;
          background-size: 46% auto;
        }
        .homepage-cta h2 {
          width: 100% !important;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 10rem 6rem;
        }
        .page-template-home .buses-home {
          padding: 5.5rem 2.2rem 5rem;
        }
        .page-template-home .buses-cards {
          background-color: #fafafa;
          padding: 2.5rem 1rem 1rem;
        }
        .buses-home h2 {
          width: 100%;
          padding-bottom: 35px;
        }
        .light-banner-home {
          padding-left: 3rem;
          padding-right: 3rem;
        }
        .light-banner-section-home p,
        h2 {
          width: 100%;
        }
        .why-rent-image {
          padding-top: 4rem;
        }
        .page-template-home .home-service h2 {
          width: 80%;
        }
        .page-template-home .service-card h2 a {
          color: #333;
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .bus-card {
          padding: 16px 16px 20px;
        }
        .buses-cards h3 a {
          font-size: 22px;
          line-height: 25px;
        }
        .bus-buttons {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
        .hero-box {
          padding: 0 2.5rem;
          margin-top: -77px;
        }
        .hero-boxes {
          padding: 0 10px;
        }
        .hero-box p {
          font-size: 22px;
          line-height: 25px;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 28px;
        }
        .amenities .amenities-title {
          font-size: 22px;
          line-height: 25px;
          padding-bottom: 22px;
        }
        h2.desktop {
          font-size: 28px;
          line-height: 32px;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .price-table {
          padding: 0 0 38px;
        }
        .page-template-home .prices {
          padding: 4rem 2rem;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 16px;
          text-align: center;
          padding-bottom: 34.4px;
          margin: auto;
        }
        .table-title th {
          font-size: 22px;
          line-height: 25px;
        }
        .sub-buses-2 {
          padding-left: 40px;
        }
      }
      @media (min-width: 1171px) and (max-width: 1220px) {
        .container {
          max-width: 1160px !important;
          padding: 0;
        }
        h1 {
          font-size: 48px;
          line-height: 46px;
        }
        h2 {
          font-size: 50px;
          line-height: 46px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
          min-width: 153px;
        }
        .navbar-brand img {
          height: 52px !important;
          min-width: 175px;
        }
        .nav-item {
          margin-left: 3px;
          margin-right: 3px;
        }
        .e-fas-arrow-right {
          margin-left: 10px;
          margin-bottom: 2px;
          width: 14px;
          height: 14px;
        }
        .dropdown-item {
          font-size: 14px !important;
          line-height: 20px;
        }
        .nav-link {
          font-size: 14px;
          line-height: 20px;
        }
        .footer {
          padding: 5rem 4rem 3rem;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .footer-cta .footer-dark {
          padding: 4.5rem 3rem;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
        }
        .footer {
          background-color: var(--theme-light-color) !important;
        }
        .footer .footer-link {
          font-size: 14px;
          line-height: 20px;
        }
        .dark-footer img {
          width: 40%;
          margin: auto;
        }
        .btn {
          font-size: 16px;
        }
        .light-cta-section {
          padding-top: 3rem;
        }
        .light-cta-section p.heading {
          font-size: 22px;
          line-height: 25px;
        }
        .light-cta-section p {
          font-size: 18px;
          font-weight: 500;
        }
        .client-title {
          font-size: 22px;
          line-height: 25px;
        }
        .clients-logos {
          padding: 4rem 1rem 1.5rem;
        }
        .testimonial p {
          font-size: 14px;
        }
        .testimonial .repeater-item img {
          width: 20%;
        }
        .testimonial .repeater-item {
          min-height: 380px !important;
        }
        .image-banner {
          padding: 0 3rem 3.5rem;
        }
        .light-banner {
          padding: 5rem 3rem;
        }
        .image-banner-home {
          background-image: none;
          padding: 0 0 9.3rem 2.5rem !important;
        }
        .home-hero-banner-button {
          padding: 0 !important;
        }
        .image-banner-home {
          background-position: top right;
          background-size: 46% auto;
        }
        .homepage-cta h2 {
          width: 100% !important;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 10rem 6rem;
        }
        .why-rent-image {
          padding-top: 4rem;
        }
        .light-banner-section-home p,
        h2 {
          width: 100%;
        }
        .page-template-home .home-service h2 {
          width: 80%;
        }
        .page-template-home .service-card h2 a {
          color: #333;
          font-size: 22px;
          line-height: 25px;
        }
        .page-template-home .buses-home {
          padding: 5.5rem 2.2rem 5rem;
        }
        .page-template-home .buses-cards {
          background-color: #fafafa;
          padding: 2.5rem 1rem 1rem;
        }
        .buses-home h2 {
          width: 100%;
          padding-bottom: 35px;
        }
        .light-banner-home {
          padding: 5rem 3rem 10rem;
        }
        .page-template-home .bus-card {
          padding: 16px 16px 20px;
        }
        .bus-buttons {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
        .hero-box {
          padding: 0 2.5rem;
          margin-top: -77px;
        }
        .hero-boxes {
          padding: 0 10px;
        }
        .hero-box p {
          font-size: 22px;
          line-height: 25px;
        }
        .buses-cards h3 a {
          font-size: 22px;
          line-height: 25px;
        }
        .amenities-card-section {
          margin: 10px 0;
        }
        .amenities .amenities-title {
          font-size: 22px;
          line-height: 25px;
          padding-bottom: 22px;
        }
        h2.desktop {
          font-size: 28px;
          line-height: 32px;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2 {
          font-size: 22px;
          line-height: 25px;
        }
        .table-title th {
          font-size: 22px;
          line-height: 25px;
        }
        .sub-buses-2 {
          padding-left: 40px;
        }
      }
      @media (min-width: 1024px) and (max-width: 1170px) {
        .container {
          max-width: 1080px !important;
          padding: 0;
        }
        h1 {
          font-size: 48px;
          line-height: 44px;
        }
        h2 {
          font-size: 46px;
          line-height: 55px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
          min-width: 110px;
        }
        .navbar-brand img {
          height: 65px !important;
        }
        .nav-item {
          margin-left: 4px;
          margin-right: 4px;
        }
        .nav-link {
          font-size: 14px !important;
          line-height: 20px !important;
          padding: 12px 6px !important;
        }
        .dropdown-item {
          font-size: 14px !important;
          line-height: 20px;
        }
        .nav-link {
          font-size: 14px;
          line-height: 20px;
        }
        .header-button .btn-light {
          padding: 7px 5px 7px 1px !important;
        }
        .footer {
          padding: 5rem 3rem 3rem;
        }
        .footer-cta {
          padding-left: 3rem;
          padding-right: 3rem;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
        }
        .footer-cta .footer-dark {
          padding: 4.5rem 3rem;
          width: 75%;
        }
        .footer-cta .footer-light {
          width: 25%;
        }
        .footer-quote {
          font-size: 24px;
          line-height: 30px;
        }
        .dark-footer img {
          width: 40%;
          margin: auto;
        }
        .btn {
          font-size: 16px;
        }
        .btn-dark.desktop {
          font-size: 14px !important;
        }
        .btn-light.desktop {
          font-size: 14px !important;
        }
        .header-button .btn-dark {
          display: none !important;
        }
        .e-fas-arrow-right {
          margin-left: 10px;
          margin-bottom: 2px;
          width: 14px;
          height: 14px;
        }
        .hero-banner .banner-button .btn-dark {
          padding: 4px 70px 2px 3px !important;
        }
        .hero-banner .banner-button .btn-light {
          padding: 9.5px 39px !important;
        }
        .hero-banner .banner-button .call-svg {
          margin-right: 30px !important;
          margin-bottom: 1px;
        }
        .banner-button .btn-dark {
          margin-bottom: 20px !important;
        }
        .light-cta-section {
          padding-top: 3rem;
        }
        .light-cta-section p.heading {
          font-size: 20px;
          line-height: 22px;
        }
        .light-cta-section p {
          font-size: 16px;
          font-weight: 500;
        }
        .client-title {
          font-size: 20px;
          line-height: 22px;
        }
        .clients-logos {
          padding: 4rem 0 2.5rem 2rem;
        }
        .testimonial-message {
          font-size: 14px;
        }
        .testimonial .repeater-item img {
          width: 20%;
        }
        .testimonial .repeater-item {
          min-height: 420px !important;
        }
        .image-banner {
          padding: 0 2rem 3.5rem;
        }
        .light-banner {
          padding: 4rem 2rem;
        }
        .image-banner-home {
          padding: 0 3rem 9.3rem !important;
        }
        .home-hero-banner-button {
          padding: 0 !important;
        }
        .image-banner-home {
          background-position: top right;
          background-size: 46% auto;
        }
        .image-banner-home .btn-dark {
          margin-bottom: 20px !important;
        }
        .homepage-cta h2 {
          width: 100% !important;
        }
        .light-banner-home {
          padding: 5rem 3rem 10rem;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 10rem 6rem;
        }
        .why-rent-image {
          padding-top: 4rem;
        }
        .light-banner-section-home p,
        h2 {
          width: 100%;
        }
        .page-template-home .home-service h2 {
          width: 100%;
        }
        .page-template-home .service-card h2 a {
          color: #333;
          font-size: 20px;
          line-height: 22px;
        }
        .page-template-home .service-card {
          padding: 0 20px;
        }
        .page-template-home .home-service {
          padding: 5rem 2rem 0;
        }
        .page-template-home .buses-home {
          padding: 5.5rem 2.2rem 5rem;
        }
        .page-template-home .buses-cards {
          background-color: #fafafa;
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h3 a {
          font-size: 22px;
          line-height: 25px;
        }
        .buses-home h2 {
          width: 100%;
          padding-bottom: 35px;
        }
        .page-template-home .bus-card {
          padding: 16px 16px 20px;
        }
        .hero-box {
          padding: 0 2.5rem;
          margin-top: -77px;
        }
        .hero-boxes {
          padding: 0;
        }
        .hero-box p {
          font-size: 20px;
          line-height: 22px;
        }
        .hero-box-banner {
          background-color: #fff;
          box-shadow: 0 4px 10px -2px rgba(0, 0, 0, 0.5);
          padding: 20px 25px;
          margin: 0 6px;
          border-radius: 16px;
          box-sizing: content-box;
          height: 80%;
        }
        .buses-cards h3 a {
          font-size: 20px;
          line-height: 22px;
        }
        .bus-buttons {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
        .amenities-card-section {
          margin: 10px 0;
        }
        .amenities .amenities-title {
          font-size: 20px;
          line-height: 22px;
          padding-bottom: 22px;
        }
        .amenities-card {
          padding: 24px 16px;
          margin: 0 8px 25px;
        }
        .amenities {
          padding-bottom: 58px;
          padding-top: 58px;
          padding-left: 100px;
          padding-right: 100px;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2,
        .buses-cards h3 {
          font-size: 20px;
          line-height: 22px;
        }
        .page-template-home .price-table {
          padding: 0 0 38px;
        }
        .page-template-home .prices {
          padding: 5rem 3rem;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 16px;
          text-align: center;
          padding-bottom: 34.4px;
          margin: auto;
        }
        .table-title th {
          font-size: 20px;
          line-height: 22px;
        }
        td {
          font-size: 16px;
          line-height: 20px;
        }
        .sub-buses-2 {
          padding-left: 40px;
        }
      }
      @media (min-width: 993px) and (max-width: 1023px) {
        .container {
          max-width: 1000px !important;
          padding: 0;
        }
        h1 {
          font-size: 48px;
          line-height: 44px;
        }
        h2 {
          font-size: 46px;
          line-height: 55px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
        }
        .navbar-nav .dropdown:hover .dropdown-menu {
          display: block;
          margin-top: 0 !important;
          min-width: 100px;
        }
        .navbar-brand img {
          height: 55px !important;
          min-width: 175px;
        }
        .nav-item {
          margin-left: 4px;
          margin-right: 4px;
        }
        .nav-link {
          font-size: 14px !important;
          line-height: 20px !important;
          padding: 12px 6px !important;
        }
        .dropdown-item {
          font-size: 14px !important;
          line-height: 20px;
        }
        .nav-link {
          font-size: 14px;
          line-height: 20px;
        }
        .footer {
          padding: 5rem 3rem 3rem;
        }
        .footer-cta {
          padding-left: 3rem;
          padding-right: 3rem;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
        }
        .footer-cta .footer-dark {
          padding: 4.5rem 3rem;
          width: 75%;
        }
        .footer-cta .footer-light {
          width: 25%;
        }
        .footer-dark-cta .btn-dark {
          margin-bottom: 0 !important;
        }
        .footer-quote {
          font-size: 24px;
          line-height: 30px;
        }
        .dark-footer img {
          width: 60%;
          margin: auto;
        }
        .btn {
          font-size: 14px;
        }
        .btn-dark.desktop {
          font-size: 14px;
        }
        .btn-light.desktop {
          font-size: 14px;
        }
        .e-fas-arrow-right {
          margin-left: 10px;
          margin-bottom: 2px;
          width: 14px;
          height: 14px;
        }
        .hero-banner .banner-button .btn-dark {
          padding: 4px 70px 2px 3px !important;
        }
        .hero-banner .banner-button .btn-light {
          padding: 9.5px 39px !important;
        }
        .hero-banner .banner-button .call-svg {
          margin-right: 30px !important;
          margin-bottom: 1px;
        }
        .header-button .btn-dark {
          display: none !important;
        }
        .banner-button .btn-dark {
          margin-bottom: 20px !important;
        }
        .light-cta-section {
          padding-top: 1rem;
        }
        .light-cta-section p.heading {
          font-size: 20px;
          line-height: 22px;
        }
        .light-cta-section p {
          font-size: 16px;
          font-weight: 500;
        }
        .client-title {
          font-size: 20px;
          line-height: 22px;
        }
        .clients-logos {
          padding: 4rem 0 2.5rem 2rem;
        }
        .testimonial-message {
          font-size: 14px;
        }
        .testimonial .repeater-item img {
          width: 20%;
        }
        .testimonial .repeater-item {
          min-height: 425px !important;
        }
        .image-banner {
          padding: 0 2rem 3.5rem;
        }
        .banner-button .btn-dark {
          margin-bottom: 20px;
        }
        .light-banner {
          padding: 4rem 2rem;
        }
        .page-template-home .home-hero-banner-button {
          padding: 0 !important;
        }
        .image-banner-home {
          background-position: top right;
          padding: 0.5rem 2.5rem 9rem;
        }
        .homepage-cta h2 {
          width: 100% !important;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 10rem 6rem;
        }
        .page-template-home .buses-home {
          padding: 5.5rem 2.2rem 5rem;
        }
        .hero-box-banner {
          background-color: #fff;
          box-shadow: 0 4px 10px -2px rgba(0, 0, 0, 0.5);
          padding: 20px;
          margin: 0;
          border-radius: 16px;
          box-sizing: content-box;
          height: 80%;
        }
        .page-template-home .buses-cards {
          background-color: #fafafa;
          padding: 2.5rem 1rem 1rem;
        }
        .buses-home h2 {
          width: 100%;
          padding-bottom: 35px;
        }
        .page-template-home .bus-card {
          padding: 16px 16px 44px;
        }
        .hero-box {
          padding: 0 2.5rem;
          margin-top: -65px;
        }
        .hero-boxes {
          padding: 0 10px;
        }
        .why-rent-image {
          padding: 7rem 0;
        }
        .light-banner-home {
          padding: 5rem 2rem 7rem;
        }
        .hero-box p {
          font-size: 20px;
          line-height: 22px;
        }
        .page-template-home .home-service {
          padding-top: 5rem;
        }
        .page-template-home .service-card h2 a {
          color: #333;
          font-size: 20px;
          line-height: 22px;
        }
        .footer-dark-cta .btn-dark {
          margin-bottom: 20px;
        }
        .bus-buttons {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
        .amenities-card-section {
          margin: 10px 0;
        }
        .amenities .amenities-title {
          font-size: 20px;
          line-height: 22px;
          padding-bottom: 22px;
        }
        .amenities-card {
          padding: 24px 16px;
          margin: 0 8px 25px;
        }
        .amenities {
          padding-bottom: 58px;
          padding-top: 58px;
          padding-left: 100px;
          padding-right: 100px;
        }
        .buses-cards h2,
        .buses-cards h3 {
          font-size: 20px;
          line-height: 22px;
        }
        .page-template-home .price-table {
          padding: 0 0 38px;
        }
        .page-template-home .prices {
          padding: 5rem 3rem;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 16px;
          text-align: center;
          padding-bottom: 34.4px;
          margin: auto;
        }
        .table-title th {
          font-size: 20px;
          line-height: 22px;
        }
        td {
          font-size: 16px;
          line-height: 20px;
        }
        .sub-buses-2 {
          padding-left: 40px;
        }
      }
      @media (min-width: 768px) and (max-width: 992px) {
        .container {
          max-width: 940px !important;
          padding: 0;
        }
        .desktop {
          display: none;
        }
        .tablet {
          display: block;
        }
        .mobile {
          display: block;
        }
        .btn-light.desktop {
          display: none;
        }
        h1 {
          font-size: 48px;
          line-height: 44px;
        }
        h2 {
          font-size: 46px;
          line-height: 55px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
        }
        .navbar-brand img {
          height: 44px !important;
          min-width: 153px;
        }
        .nav-item {
          margin-left: 1px;
          margin-right: 1px;
        }
        .dropdown-item {
          font-size: 14px !important;
          line-height: 20px;
        }
        .nav-link {
          font-size: 14px;
          line-height: 20px;
        }
        .footer {
          padding: 5rem 2rem 3rem;
        }
        .container.footer-dark-cta {
          max-width: 992px;
        }
        .footer-cta {
          padding-left: 2rem;
          padding-right: 2rem;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .footer .footer-logo {
          width: 25%;
          height: auto;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
        }
        .footer-dark .btn-dark {
          margin-bottom: 20px;
        }
        .footer-cta .footer-dark {
          padding: 3.5rem 3rem;
        }
        .footer-quote {
          font-size: 24px;
          line-height: 30px;
        }
        .footer-cta .review-image {
          width: 35%;
        }
        .footer-cta .review-title {
          font-size: 14px;
          line-height: 20px;
        }
        .footer-cta .footer-quote {
          font-size: 24px;
          line-height: 30px;
        }
        .footer-cta .footer-light {
          width: 30%;
        }
        .footer-sticky.visible {
          transform: translateY(0);
          display: block;
        }
        .dark-footer img {
          width: 60%;
          margin: auto;
        }
        .btn {
          font-size: 16px;
        }
        .view-bus-button {
          padding: 13px !important;
        }
        .e-fas-arrow-right {
          margin-left: 10px;
          margin-bottom: 2px;
          width: 14px;
          height: 14px;
        }
        .hero-banner .banner-button .btn-dark {
          padding: 4px 70px 2px 3px !important;
        }
        .hero-banner .banner-button .btn-light {
          padding: 9.5px 39px !important;
        }
        .hero-banner .banner-button .call-svg {
          margin-right: 30px !important;
          margin-bottom: 1px;
        }
        .light-cta-section {
          padding-top: 1rem;
        }
        .light-cta-section p.heading {
          font-size: 20px;
          line-height: 22px;
        }
        .light-cta-section p {
          font-size: 16px;
          font-weight: 500;
        }
        .client-title {
          font-size: 20px;
          line-height: 22px;
        }
        .clients-logos {
          padding: 4rem 0 2.5rem 2rem;
        }
        .testimonial-message {
          font-size: 14px;
        }
        .testimonial .repeater-item {
          margin-bottom: 25px;
        }
        .testimonial .repeater-item {
          height: 320px;
        }
        .image-banner {
          padding: 0 0 9.3rem 2.5rem;
        }
        .banner-button .btn-dark {
          margin-bottom: 20px;
        }
        .light-banner {
          padding: 4rem 2rem;
        }
        .page-template-home .home-hero-banner-button {
          padding: 0 !important;
        }
        .image-banner-home {
          background-position: top right;
          background-size: 46% auto;
          padding: 0.5rem 2rem 9rem;
        }
        .homepage-cta h2 {
          width: 100% !important;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 0 6rem;
        }
        .page-template-home .buses-home {
          padding: 5.5rem 2rem 5rem;
        }
        .page-template-home .buses-cards {
          background-color: #fafafa;
          padding: 2.5rem 1rem 1rem;
        }
        .buses-home h2 {
          width: 100%;
          padding-bottom: 35px;
        }
        .page-template-home .bus-card {
          padding: 16px 16px 44px;
        }
        .hero-box {
          padding: 0 2.5rem;
          margin-top: -78px;
        }
        .page-template-home .service-card {
          padding: 0 20px;
        }
        .page-template-home .home-service h2 {
          width: 100%;
          margin: auto;
          padding-bottom: 20px;
        }
        .page-template-home .service-card h2 a {
          color: #333;
          font-size: 20px;
          line-height: 22px;
        }
        .why-rent-image img {
          width: 100% !important;
        }
        .light-banner-home {
          padding: 5rem 2rem 7rem;
        }
        .homepage-cta .action-cta .btn-dark {
          margin-bottom: 0 !important;
          margin-right: 20px !important;
        }
        .hero-boxes {
          padding: 20px 10px;
        }
        .hero-box p {
          font-size: 22px;
          line-height: 25px;
        }
        .faq-section {
          padding: 0 2rem 4rem;
        }
        div#accordion {
          width: 100%;
          margin: auto;
        }
        .bus-buttons {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
        .amenities-card-section {
          margin: 10px 0;
        }
        .amenities .amenities-title {
          font-size: 20px;
          line-height: 22px;
          padding-bottom: 22px;
        }
        .amenities-card {
          padding: 24px 16px;
          margin: 0 8px 25px;
        }
        .amenities {
          padding-bottom: 58px;
          padding-top: 58px;
          padding-left: 100px;
          padding-right: 100px;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2,
        .buses-cards h3 {
          font-size: 20px;
          line-height: 22px;
        }
        .page-template-home .price-table {
          padding: 0 0 38px;
        }
        .page-template-home .prices {
          padding: 4rem 2rem;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 16px;
          text-align: center;
          padding-bottom: 34.4px;
          margin: auto;
        }
        .table-title th {
          font-size: 20px;
          line-height: 22px;
        }
        td {
          font-size: 16px;
          line-height: 20px;
        }
        .sub-buses-2 {
          padding-left: 0;
          padding-top: 40px;
        }
      }
      @media (min-width: 567px) and (max-width: 767px) {
        .container {
          max-width: 700px !important;
          padding: 0;
        }
        h1 {
          font-size: 32px;
          line-height: 38px;
          letter-spacing: -0.16px;
          padding-bottom: 0.5rem !important;
        }
        h2 {
          font-size: 26px;
          line-height: 30px;
          letter-spacing: -0.32px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
          letter-spacing: -0.24px;
        }
        .desktop {
          display: none;
        }
        .tablet {
          display: none;
        }
        .navbar-brand img {
          height: 44px !important;
          min-width: 100px;
        }
        .dropdown-item,
        .nav-link {
          font-size: 14px !important;
        }
        .navbar-brand img {
          height: 40px !important;
        }
        .mobile {
          display: block;
        }
        .dropdown-menu {
          width: 100%;
          box-shadow: none !important;
          border: 0 !important;
          margin-top: 0 !important;
        }
        .nav-item {
          margin: 0;
        }
        .dropdown-item,
        .nav-link {
          font-size: 14px !important;
          padding: 8px 20px;
        }
        .footer-cta,
        .footer-light-section {
          display: none;
        }
        .footer {
          padding: 1rem 1rem 2rem;
        }
        .footer .btn-dark {
          margin-bottom: 0;
        }
        .footer .footer-logo {
          width: 60%;
          height: auto;
        }
        .footer .footer-title {
          margin-top: 35px !important;
          padding-bottom: 0 !important;
          font-size: 18px !important;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
          margin-top: 2rem !important;
        }
        .footer .footer-link {
          font-size: 14px !important;
          line-height: 20px;
        }
        .dark-footer img {
          width: 80%;
          margin: auto;
        }
        .footer .btn-light {
          padding: 9.5px 115px !important;
        }
        .btn {
          font-size: 16px;
        }
        .btn-dark {
          margin-bottom: 20px;
          font-size: 16px;
          line-height: 18px;
        }
        .btn-dark {
          border: 2px solid #333;
          padding: 4px 165px 2px 2px;
          border-radius: 50px;
          background-image: none !important;
          background-color: #333;
          color: #fff;
          --bs-btn-box-shadow: none !important;
        }
        .call-svg {
          margin-right: 160px;
          margin-bottom: 1px;
        }
        .btn-light {
          font-size: 16px;
          line-height: 18px;
          margin-right: 0 !important;
        }
        .btn-link {
          text-align: start;
        }
        .btn-link {
          font-size: 14px;
        }
        .e-fas-arrow-right {
          margin-left: 10px;
          margin-bottom: 2px;
          width: 14px;
          height: 14px;
        }
        .btn.btn-dark.mobile {
          width: auto !important;
          padding: 4px 10px 2px 3px !important;
        }
        .btn.btn-dark.mobile .call-svg {
          margin-right: 10px !important;
          margin-bottom: 1px;
        }
        .price-section-buttons .btn-light {
          margin-bottom: 20px !important;
          padding: 9.5px 115px !important;
        }
        .home-hero-banner-button .btn-dark {
          padding: 4px 135px 2px 3px !important;
        }
        .home-hero-banner-button .call-svg {
          margin-right: 140px;
          margin-bottom: 1px;
        }
        .home-hero-banner-button .btn-light {
          padding: 9.5px 125px !important;
        }
        .hero-banner .banner-button .btn-dark {
          padding: 4px 124px 2px 3px !important;
        }
        .hero-banner .banner-button .call-svg {
          margin-right: 140px;
          margin-bottom: 1px;
        }
        .hero-banner .banner-button .btn-light {
          padding: 9.5px 115px !important;
        }
        .page-template-home .light-cta-button .btn-light {
          padding: 9.5px 115px !important;
        }
        .page-template-home .light-cta-button .btn-dark {
          padding: 4px 120px 2px 3px !important;
          margin-right: 0 !important;
        }
        .page-template-home .light-cta-button .call-svg {
          margin-right: 130px;
          margin-bottom: 1px;
        }
        .page-template-home .homepage-cta .btn-light {
          padding: 0 96px !important;
        }
        .page-template-home .homepage-cta .btn-dark {
          padding: 4px 120px 2px 3px !important;
          margin-right: 0 !important;
        }
        .page-template-home .homepage-cta .call-svg {
          margin-right: 130px;
          margin-bottom: 1px;
        }
        .btn-light .quote-button {
          padding: 0;
          margin-bottom: 50px;
        }
        .action-cta {
          margin-bottom: 20px;
        }
        .light-cta-section {
          padding-top: 2rem;
        }
        .light-cta-section p.heading {
          font-size: 20px;
          line-height: 22px;
          padding-top: 2rem;
        }
        .light-cta-section p {
          font-size: 16px;
          font-weight: 500;
        }
        .client-section {
          padding: 30px 0 0;
        }
        .client-title {
          font-size: 20px;
          line-height: 30px;
        }
        .clients-logos {
          text-align-last: center !important;
        }
        .clients-image {
          padding-bottom: 30px;
        }
        .testimonial .repeater-item {
          margin-bottom: 20px;
          margin-right: 0;
        }
        .testimonial-message {
          font-size: 14px;
          font-family: var(--theme-text-font), sans-serif;
        }
        .testimonial {
          padding: 0 0.5rem;
        }
        .image-banner {
          padding: 0 1rem 6rem !important;
          background-image: none !important;
        }
        .light-banner-home {
          padding-top: 4rem;
          padding-bottom: 10.8rem;
          padding-left: 1rem;
          padding-right: 1rem;
        }
        .light-banner {
          padding: 3rem 1rem;
        }
        .banner-button .btn-dark {
          margin-right: 0 !important;
        }
        .banner-button .btn-light {
          margin-right: 0 !important;
        }
        .image-banner-home {
          background-image: none !important;
          padding: 0 1rem 6rem !important;
        }
        .image-banner {
          background-image: none !important;
          padding: 0 !important;
        }
        .light-banner-section-home p,
        h2 {
          width: 100%;
        }
        .hero-boxes {
          margin-bottom: 36px;
          padding: 0 6px;
        }
        .hero-box p {
          font-size: 20px;
          line-height: 30px;
          letter-spacing: -0.176px;
          padding-left: 10;
        }
        .hero-box-banner {
          padding: 30px !important;
          height: 60% !important;
        }
        .hero-box img {
          width: 40px !important;
          height: 40px !important;
        }
        .page-template-home .home-hero-banner-button {
          padding: 0 !important;
        }
        .page-template-home .home-service {
          padding: 0.5rem 1rem;
        }
        .page-template-home .home-service h2 {
          width: 100%;
          padding-bottom: 1.5rem;
        }
        .page-template-home .service-card {
          padding: 0 6px;
        }
        .page-template-home .service-card h2 {
          font-size: 20px;
          line-height: 30px;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 0;
        }
        .page-template-home .homepage-cta h2 {
          margin-bottom: 30px;
          width: 100%;
        }
        .why-rent-image {
          margin-top: 40px;
        }
        div#accordion {
          width: 100%;
          margin: auto;
        }
        .faq-section {
          padding: 0 1rem 2rem;
        }
        .buses-home {
          padding: 1rem 1rem 3rem;
        }
        .buses-home h2 {
          width: 100%;
          margin: auto;
          text-align: center;
        }
        .bus-card {
          padding: 6px;
        }
        .buses-cards h3 {
          font-size: 20px !important;
          line-height: 30px !important;
        }
        .buses-cards p {
          font-size: 14px !important;
        }
        .bus-buttons {
          padding-bottom: 25px;
          flex-flow: wrap;
          padding-left: 10px;
          padding-right: 10px;
        }
        .bus-buttons .btn-dark {
          margin-bottom: 0;
          width: auto !important;
        }
        .view-bus-button {
          padding: 13px 20px !important;
        }
        .amenities-card-section {
          margin: 10px 0;
        }
        .amenities .amenities-title {
          font-size: 20px;
          line-height: 22px;
          padding-bottom: 22px;
        }
        .amenities {
          padding: 58px 8px;
        }
        .amenities-card {
          margin-bottom: 20px;
        }
        .sub-buses-2 {
          padding-left: 0;
          padding-top: 20px;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2,
        .buses-cards h3 {
          font-size: 20px;
          line-height: 22px;
        }
        .page-template-home .prices {
          padding: 3rem 0.5rem 2rem;
        }
        .page-template-home .price-table {
          padding: 0 0 30px;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 14px;
          line-height: 22px;
          padding-top: 20px;
        }
        .table-title th {
          font-size: 20px;
          line-height: 22px;
        }
        td {
          font-size: 16px;
          line-height: 20px;
        }
        .sub-buses-2 {
          padding-left: 0;
          padding-top: 40px;
        }
      }
      @media (min-width: 200px) and (max-width: 566px) {
        h1 {
          font-size: 32px;
          line-height: 38px;
          letter-spacing: -0.16px;
          padding-bottom: 0.5rem !important;
        }
        h2 {
          font-size: 26px;
          line-height: 30px;
          letter-spacing: -0.32px;
        }
        p {
          font-size: 16px;
          line-height: 28px;
          letter-spacing: -0.24px;
        }
        .desktop {
          display: none;
        }
        .tablet {
          display: none;
        }
        .mobile {
          display: block;
        }
        .navbar-brand img {
          height: 28px !important;
          min-width: 94px;
        }
        .navbar {
          padding: 0.7rem 0.3rem;
        }
        .dropdown-menu {
          width: 100%;
          box-shadow: none !important;
          border: 0 !important;
          margin-top: 0 !important;
        }
        .btn-dark.mobile {
          font-size: 16px !important;
          width: auto !important;
        }
        .nav-item {
          margin: 0;
          border: none;
        }
        .nav-link:focus-visible {
          -webkit-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
          box-shadow: none !important;
          outline: 0 !important;
        }
        .dropdown-item,
        .nav-link {
          font-size: 14px !important;
          padding: 8px 20px;
        }
        .footer-cta,
        .footer-light-section {
          display: none;
        }
        .footer {
          padding: 1rem 1rem 2rem;
        }
        .footer .footer-logo {
          width: 60%;
          height: auto;
        }
        .footer .btn-light {
          padding: 10px 15px 10px 10px !important;
        }
        .footer .btn-dark {
          margin-bottom: 0;
          padding: 3px 60px 2px 3px !important;
        }
        .footer .footer-title {
          margin-top: 35px !important;
          padding-bottom: 0 !important;
          font-size: 18px !important;
        }
        .footer .copyright-title {
          font-size: 14px;
          line-height: 20px;
          margin-top: 2rem !important;
        }
        .footer-sticky.visible {
          transform: translateY(0);
          display: block;
        }
        .dark-footer img {
          width: 100%;
          margin: auto;
        }
        svg#Layer_1 {
          margin-right: 0 !important;
        }
        .btn {
          font-size: 16px;
        }
        .btn-dark {
          margin-bottom: 20px;
        }
        .btn.btn-dark.mobile {
          width: auto !important;
          padding: 4px 10px 2px 3px !important;
        }
        .btn.btn-dark.mobile .call-svg {
          margin-right: 10px !important;
          margin-bottom: 1px;
        }
        .banner-button {
          text-align: center !important;
        }
        .btn-link {
          text-align: start;
        }
        .btn-link {
          font-size: 14px;
        }
        .banner-button .btn-light {
          padding: 9.5px 20px !important;
        }
        .banner-button .btn-dark {
          padding: 4px 30px 2px 3px !important;
        }
        .banner-button .call-svg {
          margin-right: 35px !important;
          margin-bottom: 1px;
        }
        .light-cta-button .btn-light {
          padding: 9.5px 20px !important;
        }
        .light-cta-button .btn-dark {
          padding: 4px 32px 2px 3px !important;
          margin-right: 0 !important;
        }
        .light-cta-button .call-svg {
          margin-right: 35px !important;
          margin-bottom: 1px;
        }
        .homepage-cta .btn-light {
          padding: 9.5px 25px !important;
        }
        .homepage-cta .btn-dark {
          padding: 3px 48px 2px 3px !important;
          margin-right: 0 !important;
        }
        .homepage-cta .call-svg {
          margin-right: 35px !important;
          margin-bottom: 1px;
        }
        .light-cta-section .btn-dark {
          padding: 4px 18px 2px 3px !important;
          margin-right: 0 !important;
        }
        .light-cta-section .btn-light {
          padding: 9.5px 18px 9.5px 10px !important;
        }
        .light-cta-section .call-svg {
          margin-right: 30px;
          margin-bottom: 1px;
        }
        .btn-dark {
          border: 2px solid #333;
          padding: 4px 64px 2px 3px;
          border-radius: 50px;
          background-image: none !important;
          background-color: #333;
          color: #fff;
          --bs-btn-box-shadow: none !important;
        }
        .call-svg {
          margin-right: 50px;
          margin-bottom: 1px;
        }
        .btn-light .quote-button {
          padding: 0;
          margin-bottom: 50px;
        }
        .action-cta {
          margin-bottom: 20px;
        }
        .light-cta-section {
          padding-top: 2rem;
        }
        .light-cta-section p.heading {
          font-size: 20px;
          line-height: 22px;
          padding-top: 2rem;
        }
        .light-cta-section p {
          font-size: 16px;
          font-weight: 500;
        }
        .client-section {
          padding: 30px 0 0;
        }
        .client-title {
          font-size: 20px;
          line-height: 30px;
        }
        .clients-logos {
          text-align-last: center !important;
        }
        .clients-image {
          padding-bottom: 30px;
          height: auto !important;
          width: auto !important;
        }
        .col-xxl-6.col-xl-6.col-lg-6.col-md-12.col-sm-12.col-xs-12.sub-buses-2
          img {
          height: auto !important;
          width: auto !important;
        }
        .testimonial .repeater-item {
          margin-bottom: 20px;
          margin-right: 0;
        }
        .testimonial-message {
          font-size: 14px;
          font-family: var(--theme-text-font), sans-serif;
        }
        .testimonial {
          padding: 0;
        }
        .image-banner {
          padding: 0 0 2.5rem;
          background-image: none !important;
        }
        .light-banner-home {
          padding-top: 4rem;
          padding-bottom: 10.8rem;
        }
        .light-banner {
          padding: 3rem 0;
        }
        .banner-button .btn-dark {
          margin-right: 0 !important;
        }
        .image-banner-home {
          background-image: none !important;
          padding: 0 0 6rem !important;
        }
        .image-banner {
          background-image: none !important;
          padding: 0 !important;
        }
        .faq-section {
          padding: 0 0 3rem;
        }
        .light-banner-section-home p,
        h2 {
          width: 100%;
        }
        .hero-boxes {
          margin-bottom: 36px;
          padding: 0 6px;
        }
        .hero-box p {
          font-size: 20px;
          line-height: 30px;
          letter-spacing: -0.176px;
          padding-left: 10;
        }
        .hero-box-banner {
          padding: 30px !important;
          height: 60% !important;
        }
        .hero-box img {
          width: 40px !important;
          height: 40px !important;
        }
        .page-template-home .home-hero-banner-button {
          padding: 0 !important;
        }
        .page-template-home .home-service {
          padding-top: 0.5rem;
        }
        .page-template-home .home-service h2 {
          width: 100%;
          padding-bottom: 1.5rem;
        }
        .page-template-home .service-card {
          padding: 0 6px;
        }
        .page-template-home .service-card h2 {
          font-size: 20px;
          line-height: 30px;
        }
        .page-template-home .homepage-cta {
          padding: 4rem 0;
        }
        .page-template-home .homepage-cta h2 {
          margin-bottom: 30px;
          width: 100%;
        }
        .why-rent-image {
          margin-top: 40px;
        }
        .buses-home {
          padding: 1rem 0 3rem;
        }
        .buses-home h2 {
          width: 100%;
          margin: auto;
          text-align: center;
        }
        .bus-card {
          padding: 6px;
        }
        .buses-cards h3 {
          font-size: 20px !important;
          line-height: 30px !important;
        }
        .buses-cards p {
          font-size: 14px !important;
        }
        .bus-buttons {
          padding-bottom: 25px;
          flex-flow: wrap;
          gap: 1rem !important;
          padding-left: 10px;
          padding-right: 10px;
        }
        .bus-buttons .btn-dark {
          margin-bottom: 0;
          width: 100% !important;
        }
        .view-bus-button {
          padding: 13px 20px !important;
        }
        .bus-buttons .btn-light {
          width: 100% !important;
        }
        .amenities-card-section {
          margin: 10px 0;
        }
        .amenities .amenities-title {
          font-size: 20px;
          line-height: 22px;
          padding-bottom: 22px;
        }
        .amenities-card {
          margin-bottom: 20px;
        }
        .sub-buses-2 {
          padding-left: 0;
          padding-top: 20px;
        }
        .btn-light.quote-button {
          padding: 0 28px 0 0 !important;
        }
        .buses-cards {
          padding: 2.5rem 1rem 1rem;
        }
        .buses-cards h2,
        .buses-cards h3 {
          font-size: 20px;
          line-height: 22px;
        }
        .page-template-home .prices {
          padding: 3rem 0.5rem 2rem;
        }
        .page-template-home .price-table {
          padding: 0 0 30px;
        }
        div#accordion {
          width: 100%;
          margin: auto;
        }
        .faq-section {
          padding: 0 0.5rem 2rem;
        }
        .page-template-home .prices p {
          width: 100%;
          font-size: 14px;
          line-height: 22px;
          padding-top: 20px;
        }
        .page-template-home .prices {
          padding: 3rem 1rem;
        }
        .table-title th {
          font-size: 20px;
          line-height: 22px;
        }
        td {
          font-size: 16px;
          line-height: 20px;
        }
        .sub-buses-2 {
          padding-left: 0;
          padding-top: 40px;
        }
      }
      @media (min-width: 200px) and (max-width: 375px) {
        .footer-sticky a {
          font-size: 14px;
          line-height: 14px;
          color: #fff !important;
        }
      }
      @media (max-width: 991px) {
        .collapse.navbar-collapse {
          max-height: 80vh;
          overflow-y: auto;
          overflow-x: hidden;
        }
      }
      @media (max-width: 768px) {
        .image-banner-home .intro-svg {
          display: none;
        }
      }
      .banner-image-home {
        text-align: center;
      }
      .cls-1 {
        fill: #fff;
      }
      .cls-3 {
        fill: #fffefd;
      }
    